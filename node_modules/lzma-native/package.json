{"name": "lzma-native", "version": "8.0.6", "engines": {"node": ">=10.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "description": "Provides bindings to the native liblzma library (.xz file format, among others)", "main": "index", "bin": {"lzmajs": "bin/lzmajs"}, "dependencies": {"node-addon-api": "^3.1.0", "node-gyp-build": "^4.2.1", "readable-stream": "^3.6.0"}, "keywords": ["lzma", "compression", "crc32", "xz", "liblzma"], "homepage": "https://github.com/addaleax/lzma-native", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/addaleax/lzma-native.git"}, "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi --electron-compat", "prepack": "[ $(ls prebuilds | wc -l) = '6' ] || (echo 'Some prebuilds are missing'; exit 1)", "test": "mocha --expose-gc -s 1000 -t 15000", "prepare": "npm run prepare-win32 || true", "prepare-win32": "cd deps && 7z x -y xz-5.2.3-windows.7z bin_i686/liblzma.dll bin_x86-64/liblzma.dll include doc/liblzma.def", "jshint": "jshint ."}, "gypfile": true, "bugs": {"url": "https://github.com/addaleax/lzma-native/issues"}, "devDependencies": {"bl": "^4.1.0", "jshint": "^2.12.0", "mocha": "^8.3.1", "prebuildify": "^5.0.0"}}