#!/bin/bash

# Remote Desktop Electron App Build Script

echo "🔨 Building Remote Desktop Electron Application"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
    print_success "Dependencies installed successfully"
else
    print_status "Dependencies already installed"
fi

# Create assets directory if it doesn't exist
if [ ! -d "assets" ]; then
    print_status "Creating assets directory..."
    mkdir -p assets
fi

# Create build directory if it doesn't exist
if [ ! -d "build" ]; then
    print_status "Creating build directory..."
    mkdir -p build
fi

# Build the application
print_status "Building Electron application..."

# Development build
if [ "$1" = "dev" ]; then
    print_status "Running in development mode..."
    NODE_ENV=development npm run electron
    exit 0
fi

# Production build
print_status "Building for production..."

# Build for current platform
npm run dist

if [ $? -eq 0 ]; then
    print_success "Build completed successfully!"
    print_status "Built files are in the 'dist' directory"
    
    # List built files
    if [ -d "dist" ]; then
        print_status "Built files:"
        ls -la dist/
    fi
    
    echo ""
    print_success "🎉 Remote Desktop Host application built successfully!"
    echo ""
    print_status "To run the application:"
    echo "  • Development: ./build-electron.sh dev"
    echo "  • Production: Open the built app in the 'dist' directory"
    echo ""
    print_status "To create a new session:"
    echo "  1. Run the host application"
    echo "  2. Click 'Create New Session'"
    echo "  3. Share the session ID with the client"
    echo "  4. Client connects via web browser at your server URL"
    
else
    print_error "Build failed!"
    exit 1
fi
