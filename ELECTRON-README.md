# 🖥️ Remote Desktop Host - Electron Application

A professional, cross-platform desktop application for hosting remote desktop sessions. Built with Electron, this app provides a native desktop experience with system tray integration, auto-launch capabilities, and comprehensive settings management.

## ✨ Features

### 🔧 **Core Functionality**
- **Native Desktop App**: Full Electron-based desktop application
- **Cross-Platform**: Works on Windows, macOS, and Linux
- **System Tray Integration**: Minimize to tray and quick access
- **Auto-Launch**: Start automatically when computer boots
- **Settings Management**: Persistent configuration storage
- **Screen Capture**: High-quality desktop streaming
- **Session Management**: Create and manage remote access sessions

### 🛡️ **Security & Privacy**
- **End-to-End Encryption**: WebRTC encrypted connections
- **Permission Controls**: Host controls all access permissions
- **Session Expiry**: Automatic session cleanup
- **System Permissions**: Proper macOS permission handling
- **Secure Storage**: Encrypted settings storage

### 🎨 **User Experience**
- **Modern UI**: Clean, intuitive interface
- **Dark/Light Theme**: Adaptive to system preferences
- **Notifications**: Desktop notifications for events
- **Menu Integration**: Native application menus
- **Keyboard Shortcuts**: Quick access to common functions

## 🚀 Quick Start

### Prerequisites
- Node.js 16 or higher
- npm or yarn
- Git

### Installation & Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd remote-desktop-app
   npm install
   ```

2. **Run in Development**
   ```bash
   npm run electron-dev
   # or
   ./build-electron.sh dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   # or
   ./build-electron.sh
   ```

## 📱 Usage Guide

### Starting the Application

1. **Launch the Host App**
   ```bash
   npm run electron
   ```

2. **Create a Session**
   - Click "Create New Session"
   - Share the 8-digit session ID
   - Wait for client connection

3. **Manage Permissions**
   - Control remote access permissions
   - Enable/disable mouse and keyboard control
   - Manage clipboard and file transfer access

### System Tray Features

- **Quick Session Creation**: Right-click tray icon → "Create Session"
- **Show/Hide Window**: Double-click tray icon
- **Settings Access**: Right-click → "Settings"
- **Quit Application**: Right-click → "Quit"

### Keyboard Shortcuts

- `Cmd/Ctrl + N`: Create new session
- `Cmd/Ctrl + E`: End current session
- `Cmd/Ctrl + ,`: Open settings
- `Cmd/Ctrl + Q`: Quit application

## ⚙️ Configuration

### Settings Panel

Access via **Menu → Settings** or `Cmd/Ctrl + ,`

#### Connection Settings
- **Server URL**: Configure signaling server endpoint
- **Port Configuration**: Custom port settings

#### Application Behavior
- **Minimize to Tray**: Hide to system tray when minimized
- **Close to Tray**: Don't quit when window is closed
- **Auto-Launch**: Start automatically on system boot

#### Notifications
- **Desktop Notifications**: Enable/disable system notifications
- **Sound Alerts**: Audio notifications for events

### Advanced Configuration

Settings are stored in:
- **macOS**: `~/Library/Application Support/Remote Desktop Host/`
- **Windows**: `%APPDATA%/Remote Desktop Host/`
- **Linux**: `~/.config/Remote Desktop Host/`

## 🔧 Development

### Project Structure

```
host/
├── main.js              # Electron main process
├── renderer/            # Renderer process files
│   ├── index.html      # Main UI
│   ├── settings.html   # Settings window
│   ├── style.css       # Styling
│   └── renderer.js     # Renderer logic
└── assets/             # Application assets
    ├── icon.png        # App icon
    ├── icon.ico        # Windows icon
    └── icon.icns       # macOS icon
```

### Build Configuration

The app uses `electron-builder` for packaging:

```json
{
  "build": {
    "appId": "com.remotedesktop.host",
    "productName": "Remote Desktop Host",
    "directories": {
      "output": "dist",
      "buildResources": "build"
    }
  }
}
```

### Development Scripts

```bash
# Development mode with hot reload
npm run electron-dev

# Build for current platform
npm run build

# Build for all platforms
npm run build-all

# Package without installer
npm run pack
```

## 📦 Building & Distribution

### Build for Current Platform

```bash
./build-electron.sh
```

### Build for Specific Platforms

```bash
# macOS
npm run build -- --mac

# Windows
npm run build -- --win

# Linux
npm run build -- --linux
```

### Distribution Files

After building, find distributables in `dist/`:
- **macOS**: `.dmg` installer and `.app` bundle
- **Windows**: `.exe` installer and portable `.exe`
- **Linux**: `.AppImage`, `.deb`, and `.rpm` packages

## 🛠️ System Requirements

### Host Computer (Running the App)
- **macOS**: 10.14 or later
- **Windows**: Windows 10 or later
- **Linux**: Ubuntu 18.04+ or equivalent
- **RAM**: 4GB minimum, 8GB recommended
- **Network**: Stable internet connection

### Required Permissions

#### macOS
- **Screen Recording**: For desktop capture
- **Accessibility**: For remote control
- **Network**: For connections

#### Windows
- **Network Access**: Windows Firewall permissions
- **UAC**: May require administrator privileges

#### Linux
- **X11/Wayland**: Display server access
- **Network**: Firewall configuration

## 🔒 Security Considerations

### Data Protection
- All screen data is transmitted via encrypted WebRTC
- No screen data is stored on servers
- Session IDs are cryptographically secure
- Settings are stored locally with encryption

### Network Security
- Direct peer-to-peer connections when possible
- STUN servers only for NAT traversal
- No data passes through signaling server
- Configurable server endpoints

### Access Control
- Host must explicitly approve connections
- Granular permission controls
- Session timeout mechanisms
- Immediate disconnect capability

## 🐛 Troubleshooting

### Common Issues

1. **App Won't Start**
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules
   npm install
   npm run electron
   ```

2. **Screen Capture Not Working**
   - Check system permissions (macOS)
   - Verify screen recording access
   - Try different screen sources

3. **Build Failures**
   ```bash
   # Clean build
   rm -rf dist
   npm run build
   ```

4. **Connection Issues**
   - Verify server URL in settings
   - Check firewall settings
   - Test with local server first

### Debug Mode

Run with debug output:
```bash
DEBUG=* npm run electron-dev
```

### Log Files

Application logs are stored in:
- **macOS**: `~/Library/Logs/Remote Desktop Host/`
- **Windows**: `%USERPROFILE%/AppData/Roaming/Remote Desktop Host/logs/`
- **Linux**: `~/.config/Remote Desktop Host/logs/`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Development Guidelines
- Follow Electron security best practices
- Test on all target platforms
- Update documentation
- Add appropriate error handling

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Electron**: Cross-platform desktop framework
- **WebRTC**: Peer-to-peer communication
- **Socket.io**: Real-time signaling
- **electron-builder**: Application packaging
- **electron-store**: Settings persistence

---

**⚠️ Important**: This application is designed for legitimate remote access purposes only. Always ensure you have proper authorization before accessing remote computers.
