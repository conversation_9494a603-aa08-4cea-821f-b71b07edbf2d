<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Remote Desktop Host</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 450px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        input[type="text"], input[type="url"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="url"]:focus {
            outline: none;
            border-color: #3498db;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        .checkbox-group label {
            margin-bottom: 0;
            font-weight: 500;
            cursor: pointer;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }

        button {
            flex: 1;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .help-text {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ecf0f1;
        }

        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .status {
            padding: 10px 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            text-align: center;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚙️ Settings</h1>
        
        <form id="settingsForm">
            <div class="section">
                <h3>🌐 Connection</h3>
                <div class="form-group">
                    <label for="serverUrl">Server URL:</label>
                    <input type="url" id="serverUrl" placeholder="http://localhost:3000">
                    <div class="help-text">The signaling server URL for remote connections</div>
                </div>
            </div>

            <div class="section">
                <h3>🖥️ Application</h3>
                <div class="checkbox-group">
                    <input type="checkbox" id="minimizeToTray">
                    <label for="minimizeToTray">Minimize to system tray</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="closeToTray">
                    <label for="closeToTray">Close to system tray (don't quit)</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="autoLaunch">
                    <label for="autoLaunch">Start automatically when computer starts</label>
                </div>
            </div>

            <div class="section">
                <h3>🔔 Notifications</h3>
                <div class="checkbox-group">
                    <input type="checkbox" id="notifications">
                    <label for="notifications">Show desktop notifications</label>
                </div>
            </div>

            <div class="button-group">
                <button type="button" class="btn-secondary" id="cancelBtn">Cancel</button>
                <button type="submit" class="btn-primary">Save Settings</button>
            </div>

            <div id="status" class="status" style="display: none;"></div>
        </form>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        class SettingsManager {
            constructor() {
                this.form = document.getElementById('settingsForm');
                this.statusDiv = document.getElementById('status');
                
                this.loadSettings();
                this.setupEventListeners();
            }

            async loadSettings() {
                try {
                    const settings = await ipcRenderer.invoke('get-settings');
                    
                    document.getElementById('serverUrl').value = settings.serverUrl;
                    document.getElementById('minimizeToTray').checked = settings.minimizeToTray;
                    document.getElementById('closeToTray').checked = settings.closeToTray;
                    document.getElementById('autoLaunch').checked = settings.autoLaunch;
                    document.getElementById('notifications').checked = settings.notifications;
                } catch (error) {
                    console.error('Error loading settings:', error);
                    this.showStatus('Error loading settings', 'error');
                }
            }

            setupEventListeners() {
                this.form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveSettings();
                });

                document.getElementById('cancelBtn').addEventListener('click', () => {
                    window.close();
                });
            }

            async saveSettings() {
                try {
                    const settings = {
                        serverUrl: document.getElementById('serverUrl').value,
                        minimizeToTray: document.getElementById('minimizeToTray').checked,
                        closeToTray: document.getElementById('closeToTray').checked,
                        autoLaunch: document.getElementById('autoLaunch').checked,
                        notifications: document.getElementById('notifications').checked
                    };

                    const result = await ipcRenderer.invoke('save-settings', settings);
                    
                    if (result.success) {
                        this.showStatus('Settings saved successfully!', 'success');
                        setTimeout(() => {
                            window.close();
                        }, 1500);
                    } else {
                        this.showStatus('Error saving settings', 'error');
                    }
                } catch (error) {
                    console.error('Error saving settings:', error);
                    this.showStatus('Error saving settings', 'error');
                }
            }

            showStatus(message, type) {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
                this.statusDiv.style.display = 'block';
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new SettingsManager();
        });
    </script>
</body>
</html>
