<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Transfer - Remote Desktop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .transfer-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #e1e5e9;
            border-radius: 12px;
            text-align: center;
            transition: all 0.3s;
        }

        .transfer-section.dragover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .upload-area {
            padding: 40px 20px;
            cursor: pointer;
        }

        .upload-icon {
            font-size: 3rem;
            color: #95a5a6;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #7f8c8d;
            margin-bottom: 10px;
        }

        .upload-hint {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        .file-input {
            display: none;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .file-icon {
            font-size: 1.5rem;
            color: #3498db;
        }

        .file-details h4 {
            margin: 0;
            color: #2c3e50;
            font-size: 1rem;
        }

        .file-details p {
            margin: 5px 0 0 0;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .file-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 4px;
            transition: width 0.3s;
            width: 0%;
        }

        .transfer-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            text-align: center;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📁 File Transfer</h1>
        
        <div class="transfer-section" id="uploadArea">
            <div class="upload-area">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Drag & Drop Files Here</div>
                <div class="upload-hint">or click to browse files</div>
                <input type="file" id="fileInput" class="file-input" multiple>
            </div>
        </div>

        <div class="file-list" id="fileList">
            <!-- Files will be added here dynamically -->
        </div>

        <div class="transfer-stats">
            <div class="stat-card">
                <div class="stat-value" id="totalFiles">0</div>
                <div class="stat-label">Files Queued</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalSize">0 MB</div>
                <div class="stat-label">Total Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="transferSpeed">0 KB/s</div>
                <div class="stat-label">Transfer Speed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="completedFiles">0</div>
                <div class="stat-label">Completed</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="startTransfer">Start Transfer</button>
            <button class="btn btn-secondary" id="pauseTransfer">Pause</button>
            <button class="btn btn-danger" id="clearAll">Clear All</button>
            <button class="btn btn-secondary" id="closeWindow">Close</button>
        </div>

        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        class FileTransferManager {
            constructor() {
                this.files = [];
                this.isTransferring = false;
                this.transferStats = {
                    totalFiles: 0,
                    totalSize: 0,
                    completedFiles: 0,
                    transferSpeed: 0
                };

                this.initializeElements();
                this.setupEventListeners();
                this.setupIpcListeners();
            }

            initializeElements() {
                this.elements = {
                    uploadArea: document.getElementById('uploadArea'),
                    fileInput: document.getElementById('fileInput'),
                    fileList: document.getElementById('fileList'),
                    startTransfer: document.getElementById('startTransfer'),
                    pauseTransfer: document.getElementById('pauseTransfer'),
                    clearAll: document.getElementById('clearAll'),
                    closeWindow: document.getElementById('closeWindow'),
                    status: document.getElementById('status'),
                    totalFiles: document.getElementById('totalFiles'),
                    totalSize: document.getElementById('totalSize'),
                    transferSpeed: document.getElementById('transferSpeed'),
                    completedFiles: document.getElementById('completedFiles')
                };
            }

            setupEventListeners() {
                // Drag and drop
                this.elements.uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.elements.uploadArea.classList.add('dragover');
                });

                this.elements.uploadArea.addEventListener('dragleave', () => {
                    this.elements.uploadArea.classList.remove('dragover');
                });

                this.elements.uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.elements.uploadArea.classList.remove('dragover');
                    this.handleFiles(e.dataTransfer.files);
                });

                // Click to browse
                this.elements.uploadArea.addEventListener('click', () => {
                    this.elements.fileInput.click();
                });

                this.elements.fileInput.addEventListener('change', (e) => {
                    this.handleFiles(e.target.files);
                });

                // Control buttons
                this.elements.startTransfer.addEventListener('click', () => this.startTransfer());
                this.elements.pauseTransfer.addEventListener('click', () => this.pauseTransfer());
                this.elements.clearAll.addEventListener('click', () => this.clearAll());
                this.elements.closeWindow.addEventListener('click', () => window.close());
            }

            setupIpcListeners() {
                ipcRenderer.on('file-transfer-progress', (event, data) => {
                    this.updateProgress(data);
                });

                ipcRenderer.on('file-transfer-complete', (event, data) => {
                    this.onTransferComplete(data);
                });

                ipcRenderer.on('file-transfer-error', (event, error) => {
                    this.showStatus(`Transfer error: ${error}`, 'error');
                });
            }

            handleFiles(fileList) {
                Array.from(fileList).forEach(file => {
                    const fileData = {
                        id: Date.now() + Math.random(),
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        path: file.path,
                        progress: 0,
                        status: 'pending'
                    };
                    
                    this.files.push(fileData);
                    this.addFileToList(fileData);
                });

                this.updateStats();
            }

            addFileToList(file) {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.id = `file-${file.id}`;

                fileItem.innerHTML = `
                    <div class="file-info">
                        <div class="file-icon">${this.getFileIcon(file.type)}</div>
                        <div class="file-details">
                            <h4>${file.name}</h4>
                            <p>${this.formatFileSize(file.size)} • ${file.status}</p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${file.progress}%"></div>
                            </div>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-danger" onclick="fileTransfer.removeFile('${file.id}')">Remove</button>
                    </div>
                `;

                this.elements.fileList.appendChild(fileItem);
            }

            getFileIcon(type) {
                if (type.startsWith('image/')) return '🖼️';
                if (type.startsWith('video/')) return '🎥';
                if (type.startsWith('audio/')) return '🎵';
                if (type.includes('pdf')) return '📄';
                if (type.includes('text')) return '📝';
                if (type.includes('zip') || type.includes('rar')) return '📦';
                return '📄';
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            updateStats() {
                this.transferStats.totalFiles = this.files.length;
                this.transferStats.totalSize = this.files.reduce((sum, file) => sum + file.size, 0);
                this.transferStats.completedFiles = this.files.filter(f => f.status === 'completed').length;

                this.elements.totalFiles.textContent = this.transferStats.totalFiles;
                this.elements.totalSize.textContent = this.formatFileSize(this.transferStats.totalSize);
                this.elements.completedFiles.textContent = this.transferStats.completedFiles;
            }

            async startTransfer() {
                if (this.files.length === 0) {
                    this.showStatus('No files to transfer', 'error');
                    return;
                }

                this.isTransferring = true;
                this.elements.startTransfer.disabled = true;
                this.showStatus('Starting file transfer...', 'info');

                try {
                    await ipcRenderer.invoke('start-file-transfer', this.files);
                } catch (error) {
                    this.showStatus(`Failed to start transfer: ${error}`, 'error');
                    this.isTransferring = false;
                    this.elements.startTransfer.disabled = false;
                }
            }

            pauseTransfer() {
                if (this.isTransferring) {
                    ipcRenderer.send('pause-file-transfer');
                    this.isTransferring = false;
                    this.elements.startTransfer.disabled = false;
                    this.showStatus('Transfer paused', 'info');
                }
            }

            removeFile(fileId) {
                this.files = this.files.filter(f => f.id !== fileId);
                document.getElementById(`file-${fileId}`).remove();
                this.updateStats();
            }

            clearAll() {
                this.files = [];
                this.elements.fileList.innerHTML = '';
                this.updateStats();
                this.showStatus('All files cleared', 'info');
            }

            updateProgress(data) {
                const file = this.files.find(f => f.id === data.fileId);
                if (file) {
                    file.progress = data.progress;
                    file.status = data.status;
                    
                    const fileElement = document.getElementById(`file-${file.id}`);
                    if (fileElement) {
                        const progressFill = fileElement.querySelector('.progress-fill');
                        const statusText = fileElement.querySelector('.file-details p');
                        
                        progressFill.style.width = `${data.progress}%`;
                        statusText.textContent = `${this.formatFileSize(file.size)} • ${data.status}`;
                    }
                }

                this.elements.transferSpeed.textContent = `${data.speed || 0} KB/s`;
                this.updateStats();
            }

            onTransferComplete(data) {
                this.isTransferring = false;
                this.elements.startTransfer.disabled = false;
                this.showStatus(`Transfer completed! ${data.filesTransferred} files transferred successfully.`, 'success');
                this.updateStats();
            }

            showStatus(message, type) {
                this.elements.status.textContent = message;
                this.elements.status.className = `status ${type}`;
                this.elements.status.style.display = 'block';

                setTimeout(() => {
                    this.elements.status.style.display = 'none';
                }, 5000);
            }
        }

        // Initialize file transfer manager
        const fileTransfer = new FileTransferManager();
    </script>
</body>
</html>
