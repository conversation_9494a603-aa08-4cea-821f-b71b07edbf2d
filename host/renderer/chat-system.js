const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class ChatSystem {
    constructor() {
        this.messages = [];
        this.isMinimized = true;
        this.unreadCount = 0;
        this.currentUser = 'Host';
        this.remoteUser = 'Client';

        this.createChatInterface();
        this.setupEventListeners();
        this.setupIpcListeners();
    }

    createChatInterface() {
        // Create chat widget
        const chatWidget = document.createElement('div');
        chatWidget.className = 'chat-widget';
        chatWidget.innerHTML = `
            <div class="chat-header" id="chatHeader">
                <div class="chat-title">
                    <span class="chat-icon">💬</span>
                    <span>Chat</span>
                    <span class="unread-badge" id="unreadBadge" style="display: none;">0</span>
                </div>
                <div class="chat-controls">
                    <button class="chat-control-btn" id="minimizeChat">−</button>
                    <button class="chat-control-btn" id="closeChat">×</button>
                </div>
            </div>
            <div class="chat-body" id="chatBody" style="display: none;">
                <div class="chat-messages" id="chatMessages">
                    <div class="welcome-message">
                        <div class="system-message">
                            💬 Chat session started. You can now communicate with the remote user.
                        </div>
                    </div>
                </div>
                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <input type="text" id="chatInput" placeholder="Type a message..." maxlength="500">
                        <button id="sendMessage" disabled>
                            <span>📤</span>
                        </button>
                    </div>
                    <div class="chat-features">
                        <button class="feature-btn" id="sendFile" title="Send File">📎</button>
                        <button class="feature-btn" id="sendScreenshot" title="Send Screenshot">📸</button>
                        <button class="feature-btn" id="clearChat" title="Clear Chat">🗑️</button>
                    </div>
                </div>
            </div>
        `;

        // Add CSS styles
        const style = document.createElement('style');
        style.textContent = `
            .chat-widget {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 350px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 1000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                border: 1px solid #e1e5e9;
                overflow: hidden;
            }

            .chat-header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 15px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: pointer;
                user-select: none;
            }

            .chat-title {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                font-size: 1rem;
            }

            .chat-icon {
                font-size: 1.2rem;
            }

            .unread-badge {
                background: #e74c3c;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 0.8rem;
                font-weight: bold;
                margin-left: 5px;
            }

            .chat-controls {
                display: flex;
                gap: 5px;
            }

            .chat-control-btn {
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                width: 24px;
                height: 24px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
                transition: background 0.3s;
            }

            .chat-control-btn:hover {
                background: rgba(255,255,255,0.3);
            }

            .chat-body {
                height: 400px;
                display: flex;
                flex-direction: column;
            }

            .chat-messages {
                flex: 1;
                overflow-y: auto;
                padding: 15px;
                background: #f8f9fa;
            }

            .message {
                margin-bottom: 15px;
                animation: slideIn 0.3s ease-out;
            }

            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .message-bubble {
                max-width: 80%;
                padding: 10px 15px;
                border-radius: 18px;
                word-wrap: break-word;
                position: relative;
            }

            .message.sent .message-bubble {
                background: #007bff;
                color: white;
                margin-left: auto;
                border-bottom-right-radius: 4px;
            }

            .message.received .message-bubble {
                background: white;
                color: #333;
                border: 1px solid #e1e5e9;
                border-bottom-left-radius: 4px;
            }

            .message-info {
                font-size: 0.75rem;
                opacity: 0.7;
                margin-top: 5px;
                text-align: right;
            }

            .message.received .message-info {
                text-align: left;
            }

            .system-message {
                text-align: center;
                color: #6c757d;
                font-size: 0.9rem;
                font-style: italic;
                padding: 10px;
                background: rgba(108, 117, 125, 0.1);
                border-radius: 8px;
                margin: 10px 0;
            }

            .welcome-message {
                margin-bottom: 20px;
            }

            .chat-input-container {
                padding: 15px;
                background: white;
                border-top: 1px solid #e1e5e9;
            }

            .chat-input-wrapper {
                display: flex;
                gap: 10px;
                margin-bottom: 10px;
            }

            #chatInput {
                flex: 1;
                padding: 10px 15px;
                border: 1px solid #e1e5e9;
                border-radius: 20px;
                outline: none;
                font-size: 14px;
                transition: border-color 0.3s;
            }

            #chatInput:focus {
                border-color: #007bff;
            }

            #sendMessage {
                background: #007bff;
                border: none;
                color: white;
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s;
            }

            #sendMessage:hover:not(:disabled) {
                background: #0056b3;
                transform: scale(1.05);
            }

            #sendMessage:disabled {
                background: #6c757d;
                cursor: not-allowed;
            }

            .chat-features {
                display: flex;
                gap: 8px;
                justify-content: center;
            }

            .feature-btn {
                background: #f8f9fa;
                border: 1px solid #e1e5e9;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;
            }

            .feature-btn:hover {
                background: #e9ecef;
                transform: translateY(-1px);
            }

            .typing-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 15px;
                color: #6c757d;
                font-style: italic;
                font-size: 0.9rem;
            }

            .typing-dots {
                display: flex;
                gap: 3px;
            }

            .typing-dot {
                width: 6px;
                height: 6px;
                background: #6c757d;
                border-radius: 50%;
                animation: typingDot 1.4s infinite;
            }

            .typing-dot:nth-child(2) {
                animation-delay: 0.2s;
            }

            .typing-dot:nth-child(3) {
                animation-delay: 0.4s;
            }

            @keyframes typingDot {
                0%, 60%, 100% {
                    transform: translateY(0);
                    opacity: 0.4;
                }
                30% {
                    transform: translateY(-10px);
                    opacity: 1;
                }
            }

            .file-message {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e1e5e9;
            }

            .file-icon {
                font-size: 1.5rem;
            }

            .file-info {
                flex: 1;
            }

            .file-name {
                font-weight: 600;
                margin-bottom: 2px;
            }

            .file-size {
                font-size: 0.8rem;
                color: #6c757d;
            }

            .file-download {
                background: #007bff;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.8rem;
            }

            .chat-widget.minimized .chat-body {
                display: none;
            }

            .chat-widget.minimized {
                width: auto;
            }
        `;
        document.head.appendChild(style);

        // Add to page
        document.body.appendChild(chatWidget);

        this.elements = {
            chatWidget,
            chatHeader: document.getElementById('chatHeader'),
            chatBody: document.getElementById('chatBody'),
            chatMessages: document.getElementById('chatMessages'),
            chatInput: document.getElementById('chatInput'),
            sendMessage: document.getElementById('sendMessage'),
            unreadBadge: document.getElementById('unreadBadge'),
            minimizeChat: document.getElementById('minimizeChat'),
            closeChat: document.getElementById('closeChat'),
            sendFile: document.getElementById('sendFile'),
            sendScreenshot: document.getElementById('sendScreenshot'),
            clearChat: document.getElementById('clearChat')
        };
    }

    setupEventListeners() {
        // Header click to toggle
        this.elements.chatHeader.addEventListener('click', (e) => {
            if (!e.target.classList.contains('chat-control-btn')) {
                this.toggleChat();
            }
        });

        // Control buttons
        this.elements.minimizeChat.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleChat();
        });

        this.elements.closeChat.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeChat();
        });

        // Message input
        this.elements.chatInput.addEventListener('input', (e) => {
            const hasText = e.target.value.trim().length > 0;
            this.elements.sendMessage.disabled = !hasText;
            
            // Send typing indicator
            if (hasText) {
                this.sendTypingIndicator();
            }
        });

        this.elements.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        this.elements.sendMessage.addEventListener('click', () => {
            this.sendMessage();
        });

        // Feature buttons
        this.elements.sendFile.addEventListener('click', () => this.sendFile());
        this.elements.sendScreenshot.addEventListener('click', () => this.sendScreenshot());
        this.elements.clearChat.addEventListener('click', () => this.clearChat());
    }

    setupIpcListeners() {
        // Listen for incoming messages
        ipcRenderer.on('chat-message-received', (event, message) => {
            this.addMessage(message, false);
        });

        ipcRenderer.on('chat-typing-indicator', (event, data) => {
            this.showTypingIndicator(data.isTyping);
        });

        ipcRenderer.on('chat-file-received', (event, fileData) => {
            this.addFileMessage(fileData, false);
        });
    }

    toggleChat() {
        this.isMinimized = !this.isMinimized;
        
        if (this.isMinimized) {
            this.elements.chatBody.style.display = 'none';
            this.elements.chatWidget.classList.add('minimized');
            this.elements.minimizeChat.textContent = '+';
        } else {
            this.elements.chatBody.style.display = 'flex';
            this.elements.chatWidget.classList.remove('minimized');
            this.elements.minimizeChat.textContent = '−';
            this.clearUnreadCount();
        }
    }

    closeChat() {
        this.elements.chatWidget.style.display = 'none';
    }

    sendMessage() {
        const text = this.elements.chatInput.value.trim();
        if (!text) return;

        const message = {
            id: Date.now(),
            text,
            timestamp: new Date(),
            sender: this.currentUser
        };

        this.addMessage(message, true);
        this.elements.chatInput.value = '';
        this.elements.sendMessage.disabled = true;

        // Send to remote user
        ipcRenderer.send('chat-send-message', message);
    }

    addMessage(message, isSent) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isSent ? 'sent' : 'received'}`;
        
        const time = new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        messageElement.innerHTML = `
            <div class="message-bubble">
                ${this.escapeHtml(message.text)}
            </div>
            <div class="message-info">
                ${isSent ? 'You' : this.remoteUser} • ${time}
            </div>
        `;

        this.elements.chatMessages.appendChild(messageElement);
        this.scrollToBottom();

        if (!isSent && this.isMinimized) {
            this.incrementUnreadCount();
        }

        this.messages.push(message);
    }

    addFileMessage(fileData, isSent) {
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isSent ? 'sent' : 'received'}`;
        
        const time = new Date().toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });

        messageElement.innerHTML = `
            <div class="message-bubble">
                <div class="file-message">
                    <div class="file-icon">${this.getFileIcon(fileData.type)}</div>
                    <div class="file-info">
                        <div class="file-name">${fileData.name}</div>
                        <div class="file-size">${this.formatFileSize(fileData.size)}</div>
                    </div>
                    ${!isSent ? '<button class="file-download" onclick="chatSystem.downloadFile(\'' + fileData.id + '\')">Download</button>' : ''}
                </div>
            </div>
            <div class="message-info">
                ${isSent ? 'You' : this.remoteUser} • ${time}
            </div>
        `;

        this.elements.chatMessages.appendChild(messageElement);
        this.scrollToBottom();

        if (!isSent && this.isMinimized) {
            this.incrementUnreadCount();
        }
    }

    sendFile() {
        ipcRenderer.send('chat-select-file');
    }

    sendScreenshot() {
        ipcRenderer.send('chat-take-screenshot');
    }

    downloadFile(fileId) {
        ipcRenderer.send('chat-download-file', fileId);
    }

    clearChat() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.elements.chatMessages.innerHTML = `
                <div class="welcome-message">
                    <div class="system-message">
                        💬 Chat cleared.
                    </div>
                </div>
            `;
            this.messages = [];
        }
    }

    sendTypingIndicator() {
        ipcRenderer.send('chat-typing-indicator', { isTyping: true });
        
        // Clear typing indicator after 3 seconds
        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            ipcRenderer.send('chat-typing-indicator', { isTyping: false });
        }, 3000);
    }

    showTypingIndicator(isTyping) {
        const existingIndicator = this.elements.chatMessages.querySelector('.typing-indicator');
        
        if (isTyping && !existingIndicator) {
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.innerHTML = `
                ${this.remoteUser} is typing
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            `;
            this.elements.chatMessages.appendChild(indicator);
            this.scrollToBottom();
        } else if (!isTyping && existingIndicator) {
            existingIndicator.remove();
        }
    }

    incrementUnreadCount() {
        this.unreadCount++;
        this.elements.unreadBadge.textContent = this.unreadCount;
        this.elements.unreadBadge.style.display = 'flex';
    }

    clearUnreadCount() {
        this.unreadCount = 0;
        this.elements.unreadBadge.style.display = 'none';
    }

    scrollToBottom() {
        this.elements.chatMessages.scrollTop = this.elements.chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getFileIcon(type) {
        if (type.startsWith('image/')) return '🖼️';
        if (type.startsWith('video/')) return '🎥';
        if (type.startsWith('audio/')) return '🎵';
        if (type.includes('pdf')) return '📄';
        if (type.includes('text')) return '📝';
        if (type.includes('zip') || type.includes('rar')) return '📦';
        return '📄';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Export for use in main renderer
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatSystem;
}
