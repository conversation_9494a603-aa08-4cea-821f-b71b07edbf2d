const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');

class SessionRecorder {
    constructor() {
        this.isRecording = false;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.startTime = null;
        this.recordingPath = null;
        this.recordingSettings = {
            quality: 'high', // high, medium, low
            includeAudio: false,
            frameRate: 30,
            bitrate: 2500000 // 2.5 Mbps
        };

        this.setupRecordingUI();
        this.loadSettings();
    }

    setupRecordingUI() {
        // Add recording controls to the main interface
        const recordingPanel = document.createElement('div');
        recordingPanel.className = 'recording-panel';
        recordingPanel.innerHTML = `
            <div class="recording-controls">
                <h4>📹 Session Recording</h4>
                <div class="recording-status" id="recordingStatus">
                    <span class="status-indicator" id="recordingIndicator"></span>
                    <span id="recordingText">Ready to record</span>
                    <span id="recordingTime">00:00:00</span>
                </div>
                <div class="recording-buttons">
                    <button id="startRecordingBtn" class="btn btn-primary">
                        <span>🔴</span> Start Recording
                    </button>
                    <button id="stopRecordingBtn" class="btn btn-danger" disabled>
                        <span>⏹️</span> Stop Recording
                    </button>
                    <button id="pauseRecordingBtn" class="btn btn-secondary" disabled>
                        <span>⏸️</span> Pause
                    </button>
                    <button id="recordingSettingsBtn" class="btn btn-secondary">
                        <span>⚙️</span> Settings
                    </button>
                </div>
                <div class="recording-info">
                    <div class="info-item">
                        <span>Quality:</span>
                        <span id="qualityDisplay">High</span>
                    </div>
                    <div class="info-item">
                        <span>Size:</span>
                        <span id="fileSizeDisplay">0 MB</span>
                    </div>
                    <div class="info-item">
                        <span>Location:</span>
                        <span id="saveLocationDisplay">Desktop</span>
                    </div>
                </div>
            </div>
        `;

        // Add CSS for recording panel
        const style = document.createElement('style');
        style.textContent = `
            .recording-panel {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            }

            .recording-panel h4 {
                margin: 0 0 15px 0;
                font-size: 1.1rem;
            }

            .recording-status {
                display: flex;
                align-items: center;
                gap: 10px;
                margin-bottom: 15px;
                padding: 10px;
                background: rgba(255,255,255,0.1);
                border-radius: 8px;
            }

            .status-indicator {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #95a5a6;
            }

            .status-indicator.recording {
                background: #e74c3c;
                animation: pulse 1s infinite;
            }

            .status-indicator.paused {
                background: #f39c12;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }

            .recording-buttons {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
                flex-wrap: wrap;
            }

            .recording-buttons .btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s;
                display: flex;
                align-items: center;
                gap: 5px;
            }

            .recording-buttons .btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .recording-info {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 10px;
                font-size: 0.9rem;
            }

            .info-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 10px;
                background: rgba(255,255,255,0.1);
                border-radius: 4px;
            }
        `;
        document.head.appendChild(style);

        // Insert the recording panel after the session info
        const sessionActive = document.getElementById('sessionActive');
        if (sessionActive) {
            sessionActive.appendChild(recordingPanel);
        }

        this.setupRecordingEventListeners();
    }

    setupRecordingEventListeners() {
        document.getElementById('startRecordingBtn').addEventListener('click', () => this.startRecording());
        document.getElementById('stopRecordingBtn').addEventListener('click', () => this.stopRecording());
        document.getElementById('pauseRecordingBtn').addEventListener('click', () => this.pauseRecording());
        document.getElementById('recordingSettingsBtn').addEventListener('click', () => this.showRecordingSettings());

        // Update recording time every second
        setInterval(() => {
            if (this.isRecording && this.startTime) {
                const elapsed = Date.now() - this.startTime;
                document.getElementById('recordingTime').textContent = this.formatTime(elapsed);
            }
        }, 1000);
    }

    async startRecording() {
        try {
            // Get the screen stream that's already being captured
            const stream = await this.getScreenStream();
            
            if (!stream) {
                throw new Error('No screen stream available');
            }

            // Configure recording options based on settings
            const options = this.getRecordingOptions();
            
            this.mediaRecorder = new MediaRecorder(stream, options);
            this.recordedChunks = [];
            this.startTime = Date.now();

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                    this.updateFileSize();
                }
            };

            this.mediaRecorder.onstop = () => {
                this.saveRecording();
            };

            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;

            this.updateRecordingUI();
            this.showNotification('Recording started', 'Session recording has begun');

            // Notify main process
            ipcRenderer.send('recording-started', {
                sessionId: this.getSessionId(),
                settings: this.recordingSettings
            });

        } catch (error) {
            console.error('Failed to start recording:', error);
            this.showNotification('Recording failed', error.message);
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.updateRecordingUI();
            
            ipcRenderer.send('recording-stopped', {
                sessionId: this.getSessionId(),
                duration: Date.now() - this.startTime
            });
        }
    }

    pauseRecording() {
        if (this.mediaRecorder && this.isRecording) {
            if (this.mediaRecorder.state === 'recording') {
                this.mediaRecorder.pause();
                document.getElementById('pauseRecordingBtn').innerHTML = '<span>▶️</span> Resume';
                document.getElementById('recordingIndicator').className = 'status-indicator paused';
                document.getElementById('recordingText').textContent = 'Recording paused';
            } else if (this.mediaRecorder.state === 'paused') {
                this.mediaRecorder.resume();
                document.getElementById('pauseRecordingBtn').innerHTML = '<span>⏸️</span> Pause';
                document.getElementById('recordingIndicator').className = 'status-indicator recording';
                document.getElementById('recordingText').textContent = 'Recording...';
            }
        }
    }

    async getScreenStream() {
        // This should get the same stream being used for screen sharing
        try {
            return await navigator.mediaDevices.getDisplayMedia({
                video: {
                    width: { ideal: 1920 },
                    height: { ideal: 1080 },
                    frameRate: { ideal: this.recordingSettings.frameRate }
                },
                audio: this.recordingSettings.includeAudio
            });
        } catch (error) {
            console.error('Error getting screen stream:', error);
            return null;
        }
    }

    getRecordingOptions() {
        const qualitySettings = {
            high: { videoBitsPerSecond: 2500000 },
            medium: { videoBitsPerSecond: 1500000 },
            low: { videoBitsPerSecond: 800000 }
        };

        return {
            mimeType: 'video/webm;codecs=vp9',
            ...qualitySettings[this.recordingSettings.quality]
        };
    }

    async saveRecording() {
        try {
            const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
            const buffer = await blob.arrayBuffer();
            
            // Generate filename with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `session-recording-${timestamp}.webm`;
            
            // Get save location from settings
            const saveLocation = await ipcRenderer.invoke('get-recordings-path');
            this.recordingPath = path.join(saveLocation, filename);

            // Save the file
            await ipcRenderer.invoke('save-recording', {
                path: this.recordingPath,
                buffer: Buffer.from(buffer)
            });

            this.showNotification('Recording saved', `Recording saved to ${this.recordingPath}`);
            
            // Show save dialog option
            this.showRecordingSavedDialog();

        } catch (error) {
            console.error('Failed to save recording:', error);
            this.showNotification('Save failed', error.message);
        }
    }

    showRecordingSavedDialog() {
        const dialog = document.createElement('div');
        dialog.className = 'recording-saved-dialog';
        dialog.innerHTML = `
            <div class="dialog-content">
                <h3>📹 Recording Saved</h3>
                <p>Your session recording has been saved successfully.</p>
                <div class="dialog-actions">
                    <button class="btn btn-primary" onclick="this.openRecordingLocation()">Open Location</button>
                    <button class="btn btn-secondary" onclick="this.playRecording()">Play Recording</button>
                    <button class="btn btn-secondary" onclick="this.closeDialog()">Close</button>
                </div>
            </div>
        `;

        // Add dialog styles
        const style = document.createElement('style');
        style.textContent = `
            .recording-saved-dialog {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }

            .dialog-content {
                background: white;
                padding: 30px;
                border-radius: 12px;
                text-align: center;
                max-width: 400px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            }

            .dialog-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-top: 20px;
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(dialog);

        // Add methods to dialog
        dialog.openRecordingLocation = () => {
            ipcRenderer.send('open-recording-location', this.recordingPath);
            dialog.remove();
        };

        dialog.playRecording = () => {
            ipcRenderer.send('play-recording', this.recordingPath);
            dialog.remove();
        };

        dialog.closeDialog = () => {
            dialog.remove();
        };
    }

    updateRecordingUI() {
        const startBtn = document.getElementById('startRecordingBtn');
        const stopBtn = document.getElementById('stopRecordingBtn');
        const pauseBtn = document.getElementById('pauseRecordingBtn');
        const indicator = document.getElementById('recordingIndicator');
        const text = document.getElementById('recordingText');

        if (this.isRecording) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            pauseBtn.disabled = false;
            indicator.className = 'status-indicator recording';
            text.textContent = 'Recording...';
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            pauseBtn.disabled = true;
            pauseBtn.innerHTML = '<span>⏸️</span> Pause';
            indicator.className = 'status-indicator';
            text.textContent = 'Ready to record';
            document.getElementById('recordingTime').textContent = '00:00:00';
        }
    }

    updateFileSize() {
        const totalSize = this.recordedChunks.reduce((size, chunk) => size + chunk.size, 0);
        const sizeInMB = (totalSize / (1024 * 1024)).toFixed(1);
        document.getElementById('fileSizeDisplay').textContent = `${sizeInMB} MB`;
    }

    formatTime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    showRecordingSettings() {
        // This would open a settings dialog for recording preferences
        ipcRenderer.send('show-recording-settings');
    }

    loadSettings() {
        // Load recording settings from storage
        ipcRenderer.invoke('get-recording-settings').then(settings => {
            if (settings) {
                this.recordingSettings = { ...this.recordingSettings, ...settings };
                this.updateSettingsDisplay();
            }
        });
    }

    updateSettingsDisplay() {
        document.getElementById('qualityDisplay').textContent = 
            this.recordingSettings.quality.charAt(0).toUpperCase() + this.recordingSettings.quality.slice(1);
    }

    getSessionId() {
        // Get current session ID from the main app
        return document.getElementById('displaySessionId')?.textContent || 'unknown';
    }

    showNotification(title, message) {
        ipcRenderer.send('show-notification', title, message);
    }
}

// Export for use in main renderer
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SessionRecorder;
}
