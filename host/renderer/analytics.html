<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connection Analytics - Remote Desktop</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 1.8rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), transparent);
            pointer-events: none;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .metric-label {
            font-size: 1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .metric-change {
            font-size: 0.9rem;
            margin-top: 5px;
            position: relative;
            z-index: 1;
        }

        .metric-change.positive {
            color: #2ecc71;
        }

        .metric-change.negative {
            color: #e74c3c;
        }

        .charts-section {
            margin-bottom: 30px;
        }

        .chart-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1976d2;
            font-size: 1.1rem;
            border: 2px dashed #90caf9;
        }

        .connection-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .detail-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #3498db;
        }

        .detail-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-excellent {
            background: #2ecc71;
        }

        .status-good {
            background: #f39c12;
        }

        .status-poor {
            background: #e74c3c;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .real-time-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #2ecc71;
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert.warning {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="real-time-indicator">
        🔴 Live Monitoring
    </div>

    <div class="container">
        <h1>📊 Connection Analytics</h1>

        <div id="alerts"></div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" id="latency">0ms</div>
                <div class="metric-label">Latency</div>
                <div class="metric-change" id="latencyChange">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="bandwidth">0 Mbps</div>
                <div class="metric-label">Bandwidth Usage</div>
                <div class="metric-change" id="bandwidthChange">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="frameRate">0 fps</div>
                <div class="metric-label">Frame Rate</div>
                <div class="metric-change" id="frameRateChange">--</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="quality">100%</div>
                <div class="metric-label">Stream Quality</div>
                <div class="metric-change" id="qualityChange">--</div>
            </div>
        </div>

        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-title">📈 Latency Over Time</div>
                <div class="chart-placeholder" id="latencyChart">
                    Real-time latency chart will be displayed here
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">📊 Bandwidth Usage</div>
                <div class="chart-placeholder" id="bandwidthChart">
                    Bandwidth usage chart will be displayed here
                </div>
            </div>
        </div>

        <div class="connection-details">
            <div class="detail-card">
                <h3>🌐 Connection Information</h3>
                <div class="detail-item">
                    <span class="detail-label">Connection Type</span>
                    <span class="detail-value" id="connectionType">WebRTC P2P</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Protocol</span>
                    <span class="detail-value" id="protocol">UDP</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Encryption</span>
                    <span class="detail-value" id="encryption">DTLS-SRTP</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Client IP</span>
                    <span class="detail-value" id="clientIP">--</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Session Duration</span>
                    <span class="detail-value" id="sessionDuration">00:00:00</span>
                </div>
            </div>

            <div class="detail-card">
                <h3>📺 Video Statistics</h3>
                <div class="detail-item">
                    <span class="detail-label">Resolution</span>
                    <span class="detail-value" id="resolution">1920x1080</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Codec</span>
                    <span class="detail-value" id="codec">VP9</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Bitrate</span>
                    <span class="detail-value" id="bitrate">2.5 Mbps</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Packets Sent</span>
                    <span class="detail-value" id="packetsSent">0</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Packets Lost</span>
                    <span class="detail-value" id="packetsLost">0 (0%)</span>
                </div>
            </div>

            <div class="detail-card">
                <h3>⚡ Performance Metrics</h3>
                <div class="detail-item">
                    <span class="detail-label">CPU Usage</span>
                    <span class="detail-value" id="cpuUsage">0%</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Memory Usage</span>
                    <span class="detail-value" id="memoryUsage">0 MB</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Network Quality</span>
                    <span class="detail-value">
                        <span class="status-indicator status-excellent"></span>
                        <span id="networkQuality">Excellent</span>
                    </span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Jitter</span>
                    <span class="detail-value" id="jitter">0ms</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Round Trip Time</span>
                    <span class="detail-value" id="rtt">0ms</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-primary" id="exportData">📊 Export Data</button>
            <button class="btn btn-secondary" id="resetStats">🔄 Reset Statistics</button>
            <button class="btn btn-secondary" id="refreshData">⚡ Refresh</button>
            <button class="btn btn-secondary" id="closeWindow">✕ Close</button>
        </div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');

        class ConnectionAnalytics {
            constructor() {
                this.metrics = {
                    latency: [],
                    bandwidth: [],
                    frameRate: [],
                    quality: []
                };
                this.startTime = Date.now();
                this.previousValues = {};

                this.initializeElements();
                this.setupEventListeners();
                this.startMonitoring();
            }

            initializeElements() {
                this.elements = {
                    latency: document.getElementById('latency'),
                    bandwidth: document.getElementById('bandwidth'),
                    frameRate: document.getElementById('frameRate'),
                    quality: document.getElementById('quality'),
                    latencyChange: document.getElementById('latencyChange'),
                    bandwidthChange: document.getElementById('bandwidthChange'),
                    frameRateChange: document.getElementById('frameRateChange'),
                    qualityChange: document.getElementById('qualityChange'),
                    alerts: document.getElementById('alerts'),
                    sessionDuration: document.getElementById('sessionDuration'),
                    exportData: document.getElementById('exportData'),
                    resetStats: document.getElementById('resetStats'),
                    refreshData: document.getElementById('refreshData'),
                    closeWindow: document.getElementById('closeWindow')
                };
            }

            setupEventListeners() {
                this.elements.exportData.addEventListener('click', () => this.exportData());
                this.elements.resetStats.addEventListener('click', () => this.resetStatistics());
                this.elements.refreshData.addEventListener('click', () => this.refreshData());
                this.elements.closeWindow.addEventListener('click', () => window.close());

                // Listen for analytics data from main process
                ipcRenderer.on('analytics-data', (event, data) => {
                    this.updateMetrics(data);
                });

                ipcRenderer.on('connection-alert', (event, alert) => {
                    this.showAlert(alert);
                });
            }

            startMonitoring() {
                // Request analytics data every second
                setInterval(() => {
                    ipcRenderer.send('request-analytics-data');
                    this.updateSessionDuration();
                }, 1000);

                // Initial data request
                ipcRenderer.send('request-analytics-data');
            }

            updateMetrics(data) {
                // Update main metrics
                this.updateMetric('latency', data.latency, 'ms');
                this.updateMetric('bandwidth', data.bandwidth, 'Mbps');
                this.updateMetric('frameRate', data.frameRate, 'fps');
                this.updateMetric('quality', data.quality, '%');

                // Update detailed information
                this.updateDetailedInfo(data);

                // Store metrics for charts
                this.storeMetricData(data);

                // Check for alerts
                this.checkForAlerts(data);
            }

            updateMetric(metric, value, unit) {
                const element = this.elements[metric];
                const changeElement = this.elements[metric + 'Change'];
                
                if (element) {
                    element.textContent = `${value}${unit}`;
                }

                // Calculate and display change
                if (this.previousValues[metric] !== undefined && changeElement) {
                    const change = value - this.previousValues[metric];
                    const changePercent = this.previousValues[metric] !== 0 ? 
                        ((change / this.previousValues[metric]) * 100).toFixed(1) : 0;
                    
                    if (change > 0) {
                        changeElement.textContent = `+${changePercent}%`;
                        changeElement.className = 'metric-change positive';
                    } else if (change < 0) {
                        changeElement.textContent = `${changePercent}%`;
                        changeElement.className = 'metric-change negative';
                    } else {
                        changeElement.textContent = '0%';
                        changeElement.className = 'metric-change';
                    }
                }

                this.previousValues[metric] = value;
            }

            updateDetailedInfo(data) {
                // Update connection details
                if (data.clientIP) document.getElementById('clientIP').textContent = data.clientIP;
                if (data.resolution) document.getElementById('resolution').textContent = data.resolution;
                if (data.codec) document.getElementById('codec').textContent = data.codec;
                if (data.bitrate) document.getElementById('bitrate').textContent = `${data.bitrate} Mbps`;
                if (data.packetsSent) document.getElementById('packetsSent').textContent = data.packetsSent;
                if (data.packetsLost) {
                    const lossRate = data.packetsSent > 0 ? 
                        ((data.packetsLost / data.packetsSent) * 100).toFixed(2) : 0;
                    document.getElementById('packetsLost').textContent = `${data.packetsLost} (${lossRate}%)`;
                }

                // Update performance metrics
                if (data.cpuUsage) document.getElementById('cpuUsage').textContent = `${data.cpuUsage}%`;
                if (data.memoryUsage) document.getElementById('memoryUsage').textContent = `${data.memoryUsage} MB`;
                if (data.jitter) document.getElementById('jitter').textContent = `${data.jitter}ms`;
                if (data.rtt) document.getElementById('rtt').textContent = `${data.rtt}ms`;

                // Update network quality
                this.updateNetworkQuality(data);
            }

            updateNetworkQuality(data) {
                const qualityElement = document.getElementById('networkQuality');
                const indicatorElement = qualityElement.previousElementSibling;
                
                let quality = 'Excellent';
                let className = 'status-excellent';

                if (data.latency > 100 || data.quality < 80) {
                    quality = 'Poor';
                    className = 'status-poor';
                } else if (data.latency > 50 || data.quality < 90) {
                    quality = 'Good';
                    className = 'status-good';
                }

                qualityElement.textContent = quality;
                indicatorElement.className = `status-indicator ${className}`;
            }

            updateSessionDuration() {
                const duration = Date.now() - this.startTime;
                const hours = Math.floor(duration / 3600000);
                const minutes = Math.floor((duration % 3600000) / 60000);
                const seconds = Math.floor((duration % 60000) / 1000);

                this.elements.sessionDuration.textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }

            storeMetricData(data) {
                const timestamp = Date.now();
                
                // Store last 100 data points for charts
                Object.keys(this.metrics).forEach(metric => {
                    if (data[metric] !== undefined) {
                        this.metrics[metric].push({ timestamp, value: data[metric] });
                        if (this.metrics[metric].length > 100) {
                            this.metrics[metric].shift();
                        }
                    }
                });
            }

            checkForAlerts(data) {
                // Check for performance issues
                if (data.latency > 150) {
                    this.showAlert({
                        type: 'warning',
                        title: 'High Latency Detected',
                        message: `Current latency is ${data.latency}ms. Connection quality may be affected.`
                    });
                }

                if (data.quality < 70) {
                    this.showAlert({
                        type: 'warning',
                        title: 'Low Stream Quality',
                        message: `Stream quality has dropped to ${data.quality}%. Check network connection.`
                    });
                }

                if (data.packetsLost && data.packetsSent && (data.packetsLost / data.packetsSent) > 0.05) {
                    this.showAlert({
                        type: 'warning',
                        title: 'Packet Loss Detected',
                        message: `${((data.packetsLost / data.packetsSent) * 100).toFixed(1)}% packet loss detected.`
                    });
                }
            }

            showAlert(alert) {
                const alertElement = document.createElement('div');
                alertElement.className = `alert ${alert.type}`;
                alertElement.innerHTML = `
                    <strong>${alert.title}</strong><br>
                    ${alert.message}
                `;

                this.elements.alerts.appendChild(alertElement);

                // Remove alert after 10 seconds
                setTimeout(() => {
                    if (alertElement.parentNode) {
                        alertElement.remove();
                    }
                }, 10000);
            }

            async exportData() {
                try {
                    const exportData = {
                        metrics: this.metrics,
                        sessionDuration: Date.now() - this.startTime,
                        timestamp: new Date().toISOString()
                    };

                    await ipcRenderer.invoke('export-analytics-data', exportData);
                    this.showAlert({
                        type: 'success',
                        title: 'Data Exported',
                        message: 'Analytics data has been exported successfully.'
                    });
                } catch (error) {
                    this.showAlert({
                        type: 'warning',
                        title: 'Export Failed',
                        message: 'Failed to export analytics data.'
                    });
                }
            }

            resetStatistics() {
                this.metrics = {
                    latency: [],
                    bandwidth: [],
                    frameRate: [],
                    quality: []
                };
                this.startTime = Date.now();
                this.previousValues = {};
                
                this.showAlert({
                    type: 'success',
                    title: 'Statistics Reset',
                    message: 'All statistics have been reset.'
                });
            }

            refreshData() {
                ipcRenderer.send('request-analytics-data');
            }
        }

        // Initialize analytics when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new ConnectionAnalytics();
        });
    </script>
</body>
</html>
