const { ipc<PERSON><PERSON><PERSON>, desktopCapturer } = require('electron');
const io = require('socket.io-client');

// Import additional feature modules
const SessionRecorder = require('./session-recorder.js');
const ChatSystem = require('./chat-system.js');

class ElectronHostRenderer {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.clientConnected = false;
        this.localStream = null;
        this.peerConnection = null;
        this.selectedScreenSource = null;
        this.sessionRecorder = null;
        this.chatSystem = null;

        this.initializeElements();
        this.setupEventListeners();
        this.setupIpcListeners();
        this.loadScreenSources();
        this.initializeAdvancedFeatures();
    }

    initializeElements() {
        this.elements = {
            createSessionBtn: document.getElementById('createSessionBtn'),
            status: document.getElementById('status'),
            sessionCreation: document.getElementById('sessionCreation'),
            sessionActive: document.getElementById('sessionActive'),
            displaySessionId: document.getElementById('displaySessionId'),
            copySessionId: document.getElementById('copySessionId'),
            connectionStatus: document.getElementById('connectionStatus'),
            statusText: document.getElementById('statusText'),
            clientInfo: document.getElementById('clientInfo'),
            pauseBtn: document.getElementById('pauseBtn'),
            endSessionBtn: document.getElementById('endSessionBtn'),
            allowControl: document.getElementById('allowControl'),
            allowClipboard: document.getElementById('allowClipboard'),
            allowFileTransfer: document.getElementById('allowFileTransfer'),
            screenSources: document.getElementById('screenSources')
        };
    }

    setupEventListeners() {
        this.elements.createSessionBtn.addEventListener('click', () => this.createSession());
        this.elements.copySessionId.addEventListener('click', () => this.copySessionId());
        this.elements.endSessionBtn.addEventListener('click', () => this.endSession());
        this.elements.pauseBtn.addEventListener('click', () => this.togglePause());

        // Permission checkboxes
        this.elements.allowControl.addEventListener('change', (e) => {
            this.updatePermissions({ allowControl: e.target.checked });
        });
        this.elements.allowClipboard.addEventListener('change', (e) => {
            this.updatePermissions({ allowClipboard: e.target.checked });
        });
        this.elements.allowFileTransfer.addEventListener('change', (e) => {
            this.updatePermissions({ allowFileTransfer: e.target.checked });
        });
    }

    setupIpcListeners() {
        ipcRenderer.on('server-connected', () => {
            console.log('Connected to server via IPC');
        });

        ipcRenderer.on('session-ready', (event, sessionId) => {
            this.sessionId = sessionId;
            this.elements.displaySessionId.textContent = sessionId;
            this.showActiveSession();
            this.updateStatus('Waiting for client connection...', 'waiting');
        });

        ipcRenderer.on('client-connected', (event, clientId) => {
            this.clientConnected = true;
            this.showClientConnected();
            this.updateStatus('Client connected, starting screen share...', 'waiting');
            this.startScreenShare();
        });

        ipcRenderer.on('client-disconnected', () => {
            this.clientConnected = false;
            this.hideClientConnected();
            this.updateStatus('Client disconnected, waiting for new connection...', 'waiting');
            this.stopScreenShare();
        });

        ipcRenderer.on('screen-share-started', () => {
            this.updateStatus('Screen sharing active', 'connected');
        });

        ipcRenderer.on('screen-share-stopped', () => {
            this.updateStatus('Screen sharing stopped', 'waiting');
        });

        ipcRenderer.on('session-ended', () => {
            this.resetUI();
        });
    }

    async loadScreenSources() {
        try {
            const sources = await ipcRenderer.invoke('get-screen-sources');
            this.displayScreenSources(sources);
        } catch (error) {
            console.error('Error loading screen sources:', error);
        }
    }

    displayScreenSources(sources) {
        this.elements.screenSources.innerHTML = '';
        
        sources.forEach((source, index) => {
            const sourceElement = document.createElement('div');
            sourceElement.className = 'screen-source';
            if (index === 0) {
                sourceElement.classList.add('selected');
                this.selectedScreenSource = source;
            }
            
            sourceElement.innerHTML = `
                <img src="${source.thumbnail.toDataURL()}" alt="${source.name}">
                <span>${source.name}</span>
            `;
            
            sourceElement.addEventListener('click', () => {
                document.querySelectorAll('.screen-source').forEach(el => 
                    el.classList.remove('selected'));
                sourceElement.classList.add('selected');
                this.selectedScreenSource = source;
            });
            
            this.elements.screenSources.appendChild(sourceElement);
        });
    }

    async createSession() {
        this.elements.createSessionBtn.disabled = true;
        this.updateStatus('Creating session...', 'waiting');

        try {
            const sessionData = await ipcRenderer.invoke('create-session');
            this.sessionId = sessionData.sessionId;
            
            // Join the session via IPC
            ipcRenderer.send('join-session', this.sessionId);
            
        } catch (error) {
            console.error('Error creating session:', error);
            this.updateStatus('Failed to create session', 'error');
            this.elements.createSessionBtn.disabled = false;
        }
    }

    async startScreenShare() {
        try {
            if (!this.selectedScreenSource) {
                throw new Error('No screen source selected');
            }

            // Get screen capture stream using Electron's desktopCapturer
            this.localStream = await navigator.mediaDevices.getUserMedia({
                audio: false,
                video: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: this.selectedScreenSource.id,
                        minWidth: 1280,
                        maxWidth: 1920,
                        minHeight: 720,
                        maxHeight: 1080,
                        minFrameRate: 15,
                        maxFrameRate: 30
                    }
                }
            });

            console.log('Screen capture started');
            this.updateStatus('Screen sharing active', 'connected');
            
        } catch (error) {
            console.error('Error starting screen share:', error);
            this.updateStatus('Failed to start screen sharing', 'error');
        }
    }

    stopScreenShare() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }
        
        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }
    }

    showActiveSession() {
        this.elements.sessionCreation.style.display = 'none';
        this.elements.sessionActive.style.display = 'block';
    }

    showClientConnected() {
        this.elements.clientInfo.style.display = 'block';
        this.updateConnectionStatus('Client Connected', true);
    }

    hideClientConnected() {
        this.elements.clientInfo.style.display = 'none';
        this.updateConnectionStatus('Waiting...', false);
    }

    updateStatus(message, type) {
        this.elements.status.textContent = message;
        this.elements.status.className = `status ${type}`;
    }

    updateConnectionStatus(text, connected) {
        this.elements.statusText.textContent = text;
        this.elements.connectionStatus.className = `status-indicator ${connected ? 'connected' : ''}`;
    }

    updatePermissions(permissions) {
        ipcRenderer.send('update-permissions', permissions);
    }

    copySessionId() {
        navigator.clipboard.writeText(this.sessionId).then(() => {
            const originalText = this.elements.copySessionId.textContent;
            this.elements.copySessionId.textContent = '✓';
            setTimeout(() => {
                this.elements.copySessionId.textContent = originalText;
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy session ID:', err);
        });
    }

    endSession() {
        if (confirm('Are you sure you want to end this session?')) {
            ipcRenderer.send('end-session');
        }
    }

    togglePause() {
        // Implementation for pausing/resuming the session
        console.log('Pause/Resume functionality to be implemented');
    }

    resetUI() {
        this.elements.sessionCreation.style.display = 'block';
        this.elements.sessionActive.style.display = 'none';
        this.elements.clientInfo.style.display = 'none';
        this.elements.createSessionBtn.disabled = false;
        this.updateStatus('Click to create a new session', '');
        this.stopScreenShare();
    }

    initializeAdvancedFeatures() {
        // Initialize session recorder
        try {
            this.sessionRecorder = new SessionRecorder();
        } catch (error) {
            console.log('Session recorder not available:', error);
        }

        // Initialize chat system
        try {
            this.chatSystem = new ChatSystem();
        } catch (error) {
            console.log('Chat system not available:', error);
        }

        // Add advanced feature buttons to the UI
        this.addAdvancedFeatureButtons();
    }

    addAdvancedFeatureButtons() {
        const sessionActive = document.getElementById('sessionActive');
        if (!sessionActive) return;

        const advancedPanel = document.createElement('div');
        advancedPanel.className = 'advanced-features-panel';
        advancedPanel.innerHTML = `
            <div class="advanced-features">
                <h4>🚀 Advanced Features</h4>
                <div class="feature-buttons">
                    <button class="feature-btn" id="openFileTransfer">
                        <span>📁</span> File Transfer
                    </button>
                    <button class="feature-btn" id="openAnalytics">
                        <span>📊</span> Analytics
                    </button>
                    <button class="feature-btn" id="openChat">
                        <span>💬</span> Chat
                    </button>
                    <button class="feature-btn" id="takeScreenshot">
                        <span>📸</span> Screenshot
                    </button>
                </div>
            </div>
        `;

        // Add CSS for advanced features
        const style = document.createElement('style');
        style.textContent = `
            .advanced-features-panel {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 20px;
                margin: 20px 0;
                border: 1px solid #e9ecef;
            }

            .advanced-features h4 {
                margin: 0 0 15px 0;
                color: #2c3e50;
                font-size: 1.1rem;
            }

            .feature-buttons {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
                gap: 10px;
            }

            .feature-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s;
                font-size: 14px;
                font-weight: 500;
                color: #495057;
            }

            .feature-btn:hover {
                background: #e9ecef;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .feature-btn span {
                font-size: 16px;
            }
        `;
        document.head.appendChild(style);

        sessionActive.appendChild(advancedPanel);

        // Setup event listeners for advanced features
        document.getElementById('openFileTransfer').addEventListener('click', () => {
            this.openFileTransfer();
        });

        document.getElementById('openAnalytics').addEventListener('click', () => {
            this.openAnalytics();
        });

        document.getElementById('openChat').addEventListener('click', () => {
            this.toggleChat();
        });

        document.getElementById('takeScreenshot').addEventListener('click', () => {
            this.takeScreenshot();
        });
    }

    openFileTransfer() {
        ipcRenderer.send('open-file-transfer-window');
    }

    openAnalytics() {
        ipcRenderer.send('open-analytics-window');
    }

    toggleChat() {
        if (this.chatSystem) {
            this.chatSystem.toggleChat();
        }
    }

    takeScreenshot() {
        ipcRenderer.send('take-screenshot');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ElectronHostRenderer();
});
