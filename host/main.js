const { app, BrowserWindow, ipcMain, desktopCapturer, screen, Menu, Tray, dialog, shell, systemPreferences } = require('electron');
const path = require('path');
// const robot = require('robotjs'); // Temporarily disabled due to compatibility issues
const io = require('socket.io-client');
const Store = require('electron-store');
const AutoLaunch = require('auto-launch');

// Initialize store for settings
const store = new Store();

// Auto-launch setup
const autoLauncher = new AutoLaunch({
    name: 'Remote Desktop Host',
    path: app.getPath('exe')
});

class RemoteDesktopHost {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.socket = null;
        this.sessionId = null;
        this.isSharing = false;
        this.serverUrl = store.get('serverUrl', 'http://localhost:3000');
        this.permissions = {
            allowControl: true,
            allowClipboard: false,
            allowFileTransfer: false
        };
        this.isMinimizedToTray = false;
    }

    async createWindow() {
        // Check for permissions on macOS
        if (process.platform === 'darwin') {
            await this.checkPermissions();
        }

        this.mainWindow = new BrowserWindow({
            width: 900,
            height: 700,
            minWidth: 600,
            minHeight: 500,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true,
                webSecurity: false
            },
            icon: this.getIconPath(),
            title: 'Remote Desktop Host',
            show: false, // Don't show until ready
            titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default'
        });

        // Load the host interface
        await this.mainWindow.loadFile(path.join(__dirname, 'renderer', 'index.html'));

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            if (process.env.NODE_ENV === 'development') {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // Handle window events
        this.mainWindow.on('minimize', () => {
            if (store.get('minimizeToTray', true)) {
                this.mainWindow.hide();
                this.isMinimizedToTray = true;
            }
        });

        this.mainWindow.on('close', (event) => {
            if (!app.isQuiting && store.get('closeToTray', true)) {
                event.preventDefault();
                this.mainWindow.hide();
                this.isMinimizedToTray = true;
            }
        });

        this.setupIpcHandlers();
        this.createTray();
        this.createMenu();
        this.connectToServer();
    }

    async checkPermissions() {
        // Check screen recording permission on macOS
        const screenAccess = systemPreferences.getMediaAccessStatus('screen');
        if (screenAccess !== 'granted') {
            const result = await dialog.showMessageBox(this.mainWindow, {
                type: 'warning',
                title: 'Screen Recording Permission Required',
                message: 'This app needs screen recording permission to share your screen.',
                detail: 'Please grant permission in System Preferences > Security & Privacy > Privacy > Screen Recording',
                buttons: ['Open System Preferences', 'Cancel'],
                defaultId: 0
            });

            if (result.response === 0) {
                shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_ScreenCapture');
            }
        }

        // Check accessibility permission for mouse/keyboard control
        if (!systemPreferences.isTrustedAccessibilityClient(false)) {
            const result = await dialog.showMessageBox(this.mainWindow, {
                type: 'warning',
                title: 'Accessibility Permission Required',
                message: 'This app needs accessibility permission to control mouse and keyboard remotely.',
                detail: 'Please grant permission in System Preferences > Security & Privacy > Privacy > Accessibility',
                buttons: ['Open System Preferences', 'Cancel'],
                defaultId: 0
            });

            if (result.response === 0) {
                shell.openExternal('x-apple.systempreferences:com.apple.preference.security?Privacy_Accessibility');
            }
        }
    }

    getIconPath() {
        if (process.platform === 'win32') {
            return path.join(__dirname, '..', 'assets', 'icon.ico');
        } else if (process.platform === 'darwin') {
            return path.join(__dirname, '..', 'assets', 'icon.icns');
        } else {
            return path.join(__dirname, '..', 'assets', 'icon.png');
        }
    }

    createTray() {
        this.tray = new Tray(this.getIconPath());

        const contextMenu = Menu.buildFromTemplate([
            {
                label: 'Show Window',
                click: () => {
                    this.mainWindow.show();
                    this.isMinimizedToTray = false;
                }
            },
            {
                label: 'Create Session',
                click: () => {
                    this.mainWindow.show();
                    this.mainWindow.webContents.send('create-session-request');
                }
            },
            { type: 'separator' },
            {
                label: 'Settings',
                click: () => {
                    this.showSettings();
                }
            },
            { type: 'separator' },
            {
                label: 'Quit',
                click: () => {
                    app.isQuiting = true;
                    app.quit();
                }
            }
        ]);

        this.tray.setContextMenu(contextMenu);
        this.tray.setToolTip('Remote Desktop Host');

        this.tray.on('double-click', () => {
            this.mainWindow.show();
            this.isMinimizedToTray = false;
        });
    }

    createMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Session',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow.webContents.send('create-session-request');
                        }
                    },
                    {
                        label: 'End Session',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => {
                            this.mainWindow.webContents.send('end-session-request');
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Settings',
                        accelerator: 'CmdOrCtrl+,',
                        click: () => {
                            this.showSettings();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Quit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.isQuiting = true;
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => {
                            this.showAbout();
                        }
                    },
                    {
                        label: 'Learn More',
                        click: () => {
                            shell.openExternal('https://github.com/yourusername/remote-desktop-app');
                        }
                    }
                ]
            }
        ];

        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    showSettings() {
        const settingsWindow = new BrowserWindow({
            width: 500,
            height: 400,
            parent: this.mainWindow,
            modal: true,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            },
            title: 'Settings'
        });

        settingsWindow.loadFile(path.join(__dirname, 'renderer', 'settings.html'));
    }

    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Remote Desktop Host',
            message: 'Remote Desktop Host',
            detail: `Version: ${app.getVersion()}\nElectron: ${process.versions.electron}\nNode: ${process.versions.node}\n\nA secure, open-source remote desktop application.`,
            buttons: ['OK']
        });
    }

    setupIpcHandlers() {
        // Create session
        ipcMain.handle('create-session', async () => {
            try {
                const response = await fetch(`${this.serverUrl}/api/create-session`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                this.sessionId = data.sessionId;
                return data;
            } catch (error) {
                console.error('Error creating session:', error);
                throw error;
            }
        });

        // Get screen sources for capture
        ipcMain.handle('get-screen-sources', async () => {
            const sources = await desktopCapturer.getSources({
                types: ['screen'],
                thumbnailSize: { width: 150, height: 150 }
            });
            return sources;
        });

        // Update permissions
        ipcMain.on('update-permissions', (event, permissions) => {
            this.permissions = { ...this.permissions, ...permissions };
        });

        // End session
        ipcMain.on('end-session', () => {
            this.endSession();
        });

        // Settings handlers
        ipcMain.handle('get-settings', () => {
            return {
                serverUrl: store.get('serverUrl', 'http://localhost:3000'),
                minimizeToTray: store.get('minimizeToTray', true),
                closeToTray: store.get('closeToTray', true),
                autoLaunch: store.get('autoLaunch', false),
                notifications: store.get('notifications', true)
            };
        });

        ipcMain.handle('save-settings', async (event, settings) => {
            store.set('serverUrl', settings.serverUrl);
            store.set('minimizeToTray', settings.minimizeToTray);
            store.set('closeToTray', settings.closeToTray);
            store.set('notifications', settings.notifications);

            this.serverUrl = settings.serverUrl;

            // Handle auto-launch
            if (settings.autoLaunch !== store.get('autoLaunch', false)) {
                if (settings.autoLaunch) {
                    await autoLauncher.enable();
                } else {
                    await autoLauncher.disable();
                }
                store.set('autoLaunch', settings.autoLaunch);
            }

            return { success: true };
        });

        // Show notification
        ipcMain.on('show-notification', (event, title, body) => {
            if (store.get('notifications', true)) {
                new Notification({ title, body }).show();
            }
        });
    }

    connectToServer() {
        this.socket = io(this.serverUrl);

        this.socket.on('connect', () => {
            console.log('Connected to server');
            this.mainWindow.webContents.send('server-connected');
        });

        this.socket.on('host-ready', (sessionId) => {
            console.log('Host ready for session:', sessionId);
            this.mainWindow.webContents.send('session-ready', sessionId);
        });

        this.socket.on('client-connected', (clientId) => {
            console.log('Client connected:', clientId);
            this.mainWindow.webContents.send('client-connected', clientId);
            this.startScreenShare();
        });

        this.socket.on('client-disconnected', () => {
            console.log('Client disconnected');
            this.mainWindow.webContents.send('client-disconnected');
            this.stopScreenShare();
        });

        // Remote control events
        this.socket.on('mouse-move', (data) => {
            if (this.permissions.allowControl) {
                this.handleRemoteMouseMove(data);
            }
        });

        this.socket.on('mouse-click', (data) => {
            if (this.permissions.allowControl) {
                this.handleRemoteMouseClick(data);
            }
        });

        this.socket.on('key-press', (data) => {
            if (this.permissions.allowControl) {
                this.handleRemoteKeyPress(data);
            }
        });
    }

    async startScreenShare() {
        this.isSharing = true;
        this.mainWindow.webContents.send('screen-share-started');
        
        // The actual screen sharing will be handled by WebRTC in the renderer process
        // This method can be used for additional setup if needed
    }

    stopScreenShare() {
        this.isSharing = false;
        this.mainWindow.webContents.send('screen-share-stopped');
    }

    handleRemoteMouseMove(data) {
        try {
            const screenSize = screen.getPrimaryDisplay().workAreaSize;
            const x = Math.round(data.x * screenSize.width);
            const y = Math.round(data.y * screenSize.height);

            console.log(`Mouse move to: ${x}, ${y}`);
            // robot.moveMouse(x, y); // Temporarily disabled

            // Alternative: Use system APIs or send to renderer for handling
            this.mainWindow.webContents.send('remote-mouse-move', { x, y });
        } catch (error) {
            console.error('Error moving mouse:', error);
        }
    }

    handleRemoteMouseClick(data) {
        try {
            const screenSize = screen.getPrimaryDisplay().workAreaSize;
            const x = Math.round(data.x * screenSize.width);
            const y = Math.round(data.y * screenSize.height);

            console.log(`Mouse click at: ${x}, ${y}, button: ${data.button}`);
            // robot.moveMouse(x, y);
            // const button = data.button === 'right' ? 'right' : 'left';
            // robot.mouseClick(button); // Temporarily disabled

            // Send to renderer for handling
            this.mainWindow.webContents.send('remote-mouse-click', { x, y, button: data.button });
        } catch (error) {
            console.error('Error clicking mouse:', error);
        }
    }

    handleRemoteKeyPress(data) {
        try {
            if (data.type === 'keydown') {
                console.log(`Key press: ${data.key}, modifiers: ctrl=${data.ctrlKey}, alt=${data.altKey}, shift=${data.shiftKey}, meta=${data.metaKey}`);

                // Send to renderer for handling
                this.mainWindow.webContents.send('remote-key-press', data);

                // const modifiers = [];
                // if (data.ctrlKey) modifiers.push('control');
                // if (data.altKey) modifiers.push('alt');
                // if (data.shiftKey) modifiers.push('shift');
                // if (data.metaKey) modifiers.push('command');

                // // Map common keys
                // let key = data.key;
                // if (key === ' ') key = 'space';
                // if (key === 'Enter') key = 'enter';
                // if (key === 'Backspace') key = 'backspace';
                // if (key === 'Tab') key = 'tab';
                // if (key === 'Escape') key = 'escape';

                // robot.keyTap(key, modifiers); // Temporarily disabled
            }
        } catch (error) {
            console.error('Error pressing key:', error);
        }
    }

    joinSession(sessionId) {
        this.sessionId = sessionId;
        this.socket.emit('host-join', sessionId);
    }

    endSession() {
        if (this.socket) {
            this.socket.disconnect();
        }
        this.stopScreenShare();
        this.sessionId = null;
        this.mainWindow.webContents.send('session-ended');
    }
}

// App event handlers
app.whenReady().then(() => {
    const hostApp = new RemoteDesktopHost();
    hostApp.createWindow();

    // Handle session joining from renderer
    ipcMain.on('join-session', (event, sessionId) => {
        hostApp.joinSession(sessionId);
    });

    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            hostApp.createWindow();
        }
    });
});

app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
    });
});
