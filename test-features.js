#!/usr/bin/env node

/**
 * Feature Testing Script for Remote Desktop Application
 * This script tests the advanced features programmatically
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class FeatureTester {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            info: '\x1b[36m',    // Cyan
            success: '\x1b[32m', // Green
            error: '\x1b[31m',   // Red
            warning: '\x1b[33m', // Yellow
            reset: '\x1b[0m'     // Reset
        };
        
        console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
    }

    async test(name, testFunction) {
        this.testResults.total++;
        this.log(`Testing: ${name}`, 'info');
        
        try {
            await testFunction();
            this.testResults.passed++;
            this.testResults.details.push({ name, status: 'PASSED' });
            this.log(`✅ PASSED: ${name}`, 'success');
        } catch (error) {
            this.testResults.failed++;
            this.testResults.details.push({ name, status: 'FAILED', error: error.message });
            this.log(`❌ FAILED: ${name} - ${error.message}`, 'error');
        }
    }

    async runAllTests() {
        this.log('🧪 Starting Feature Testing Suite', 'info');
        this.log('=====================================', 'info');

        // Test 1: File Structure
        await this.test('File Structure Integrity', () => this.testFileStructure());

        // Test 2: Dependencies
        await this.test('Required Dependencies', () => this.testDependencies());

        // Test 3: Server Connectivity
        await this.test('Server Connectivity', () => this.testServerConnectivity());

        // Test 4: Feature Files
        await this.test('Feature Files Existence', () => this.testFeatureFiles());

        // Test 5: Configuration Files
        await this.test('Configuration Files', () => this.testConfigFiles());

        // Test 6: Asset Files
        await this.test('Asset Files', () => this.testAssetFiles());

        // Test 7: Script Permissions
        await this.test('Script Permissions', () => this.testScriptPermissions());

        // Test 8: Module Imports
        await this.test('Module Import Syntax', () => this.testModuleImports());

        this.printResults();
    }

    async testFileStructure() {
        const requiredFiles = [
            'package.json',
            'server/server.js',
            'host/main.js',
            'host/renderer/index.html',
            'host/renderer/renderer.js',
            'host/renderer/style.css',
            'client/index.html',
            'client/js/client.js',
            'client/js/webrtc.js'
        ];

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Missing required file: ${file}`);
            }
        }
    }

    async testDependencies() {
        const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
        const requiredDeps = [
            'express',
            'socket.io',
            'socket.io-client',
            'electron',
            'electron-store',
            'uuid'
        ];

        const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
        
        for (const dep of requiredDeps) {
            if (!allDeps[dep]) {
                throw new Error(`Missing dependency: ${dep}`);
            }
        }
    }

    async testServerConnectivity() {
        return new Promise((resolve, reject) => {
            const http = require('http');
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: '/',
                method: 'GET',
                timeout: 5000
            };

            const req = http.request(options, (res) => {
                if (res.statusCode === 200) {
                    resolve();
                } else {
                    reject(new Error(`Server returned status ${res.statusCode}`));
                }
            });

            req.on('error', (err) => {
                reject(new Error(`Server connection failed: ${err.message}`));
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Server connection timeout'));
            });

            req.end();
        });
    }

    async testFeatureFiles() {
        const featureFiles = [
            'host/renderer/file-transfer.html',
            'host/renderer/session-recorder.js',
            'host/renderer/analytics.html',
            'host/renderer/chat-system.js',
            'host/renderer/settings.html'
        ];

        for (const file of featureFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Missing feature file: ${file}`);
            }
            
            // Check file size (should not be empty)
            const stats = fs.statSync(file);
            if (stats.size === 0) {
                throw new Error(`Feature file is empty: ${file}`);
            }
        }
    }

    async testConfigFiles() {
        const configFiles = [
            'build/entitlements.mac.plist',
            'TESTING-GUIDE.md',
            'ADVANCED-FEATURES.md',
            'ELECTRON-README.md'
        ];

        for (const file of configFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Missing config file: ${file}`);
            }
        }
    }

    async testAssetFiles() {
        const assetFiles = [
            'assets/icon.svg'
        ];

        // Create assets directory if it doesn't exist
        if (!fs.existsSync('assets')) {
            fs.mkdirSync('assets', { recursive: true });
        }

        for (const file of assetFiles) {
            if (!fs.existsSync(file)) {
                this.log(`Warning: Asset file missing: ${file}`, 'warning');
                // Don't fail the test for missing assets, just warn
            }
        }
    }

    async testScriptPermissions() {
        const scripts = [
            'launch-remote-desktop.sh',
            'build-electron.sh',
            'start.sh'
        ];

        for (const script of scripts) {
            if (fs.existsSync(script)) {
                const stats = fs.statSync(script);
                // Check if file is executable (on Unix systems)
                if (process.platform !== 'win32' && !(stats.mode & parseInt('111', 8))) {
                    throw new Error(`Script not executable: ${script}`);
                }
            }
        }
    }

    async testModuleImports() {
        const mainFile = 'host/renderer/renderer.js';
        if (fs.existsSync(mainFile)) {
            const content = fs.readFileSync(mainFile, 'utf8');
            
            // Check for required imports
            const requiredImports = [
                'ipcRenderer',
                'SessionRecorder',
                'ChatSystem'
            ];

            for (const importName of requiredImports) {
                if (!content.includes(importName)) {
                    throw new Error(`Missing import in renderer.js: ${importName}`);
                }
            }
        }
    }

    printResults() {
        this.log('=====================================', 'info');
        this.log('🎯 Test Results Summary', 'info');
        this.log('=====================================', 'info');
        
        this.log(`Total Tests: ${this.testResults.total}`, 'info');
        this.log(`Passed: ${this.testResults.passed}`, 'success');
        this.log(`Failed: ${this.testResults.failed}`, this.testResults.failed > 0 ? 'error' : 'success');
        
        const successRate = ((this.testResults.passed / this.testResults.total) * 100).toFixed(1);
        this.log(`Success Rate: ${successRate}%`, successRate >= 80 ? 'success' : 'warning');

        if (this.testResults.failed > 0) {
            this.log('\n❌ Failed Tests:', 'error');
            this.testResults.details
                .filter(test => test.status === 'FAILED')
                .forEach(test => {
                    this.log(`  • ${test.name}: ${test.error}`, 'error');
                });
        }

        this.log('\n✅ Passed Tests:', 'success');
        this.testResults.details
            .filter(test => test.status === 'PASSED')
            .forEach(test => {
                this.log(`  • ${test.name}`, 'success');
            });

        // Overall result
        if (this.testResults.failed === 0) {
            this.log('\n🎉 All tests passed! The application is ready for use.', 'success');
        } else if (successRate >= 80) {
            this.log('\n⚠️  Most tests passed. Minor issues detected.', 'warning');
        } else {
            this.log('\n🚨 Multiple test failures. Please review the issues above.', 'error');
        }
    }
}

// Manual Testing Instructions
function printManualTestingInstructions() {
    console.log('\n🧪 MANUAL TESTING INSTRUCTIONS');
    console.log('==============================\n');
    
    console.log('Now that the automated tests have run, please test these features manually:\n');
    
    console.log('1. 🖥️  ELECTRON APP TESTING:');
    console.log('   • The Electron app should be running');
    console.log('   • Click "Create New Session" to generate a session ID');
    console.log('   • Verify the "🚀 Advanced Features" panel appears');
    console.log('   • Test each feature button:\n');
    
    console.log('2. 📁 FILE TRANSFER:');
    console.log('   • Click "📁 File Transfer" button');
    console.log('   • Drag files into the transfer area');
    console.log('   • Click "Start Transfer" and watch progress');
    console.log('   • Test pause/resume functionality\n');
    
    console.log('3. 📊 ANALYTICS:');
    console.log('   • Click "📊 Analytics" button');
    console.log('   • Verify metrics are updating (simulated data)');
    console.log('   • Check all metric cards show values');
    console.log('   • Test export and refresh buttons\n');
    
    console.log('4. 💬 CHAT SYSTEM:');
    console.log('   • Click "💬 Chat" button');
    console.log('   • Chat widget should appear in bottom-right');
    console.log('   • Type messages and test send functionality');
    console.log('   • Test file sharing and screenshot features\n');
    
    console.log('5. 🎥 SESSION RECORDING:');
    console.log('   • Look for recording controls in the session panel');
    console.log('   • Click "🔴 Start Recording"');
    console.log('   • Test pause/resume/stop functionality');
    console.log('   • Verify recording saves to Videos folder\n');
    
    console.log('6. ⚙️  SETTINGS:');
    console.log('   • Open settings via menu or Cmd/Ctrl + ,');
    console.log('   • Test all setting toggles');
    console.log('   • Verify settings save and persist\n');
    
    console.log('7. 🌐 WEB CLIENT:');
    console.log('   • Open http://localhost:3000 in browser');
    console.log('   • Enter the session ID from Electron app');
    console.log('   • Test connection and remote control\n');
    
    console.log('✅ If all features work as described, the application is fully functional!');
}

// Run the tests
if (require.main === module) {
    const tester = new FeatureTester();
    tester.runAllTests().then(() => {
        printManualTestingInstructions();
    });
}

module.exports = FeatureTester;
