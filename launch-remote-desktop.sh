#!/bin/bash

# Remote Desktop Application Launcher
# This script starts both the server and the Electron host application

echo "🖥️  Remote Desktop Application Launcher"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        print_error "Failed to install dependencies"
        exit 1
    fi
fi

# Function to start the server
start_server() {
    print_status "Starting signaling server..."
    npm start &
    SERVER_PID=$!
    sleep 3
    
    # Check if server started successfully
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        print_success "Signaling server started successfully (PID: $SERVER_PID)"
        return 0
    else
        print_error "Failed to start signaling server"
        return 1
    fi
}

# Function to start the Electron app
start_electron() {
    print_status "Starting Electron host application..."
    npm run electron &
    ELECTRON_PID=$!
    print_success "Electron application started (PID: $ELECTRON_PID)"
}

# Function to cleanup processes
cleanup() {
    print_warning "Shutting down applications..."
    
    if [ ! -z "$SERVER_PID" ]; then
        print_status "Stopping signaling server (PID: $SERVER_PID)..."
        kill $SERVER_PID 2>/dev/null
    fi
    
    if [ ! -z "$ELECTRON_PID" ]; then
        print_status "Stopping Electron application (PID: $ELECTRON_PID)..."
        kill $ELECTRON_PID 2>/dev/null
    fi
    
    print_success "Applications stopped successfully"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Parse command line arguments
case "$1" in
    "server-only")
        print_status "Starting server only..."
        start_server
        if [ $? -eq 0 ]; then
            print_success "Server is running at http://localhost:3000"
            print_status "Press Ctrl+C to stop"
            wait $SERVER_PID
        fi
        ;;
    "electron-only")
        print_status "Starting Electron app only..."
        start_electron
        print_status "Press Ctrl+C to stop"
        wait $ELECTRON_PID
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [option]"
        echo ""
        echo "Options:"
        echo "  (no option)    Start both server and Electron app"
        echo "  server-only    Start only the signaling server"
        echo "  electron-only  Start only the Electron host app"
        echo "  help           Show this help message"
        echo ""
        echo "URLs:"
        echo "  Server:        http://localhost:3000"
        echo "  Client:        http://localhost:3000"
        echo "  Host Web:      http://localhost:3000/host"
        exit 0
        ;;
    *)
        print_status "Starting complete Remote Desktop system..."
        echo ""
        
        # Start server first
        start_server
        if [ $? -ne 0 ]; then
            exit 1
        fi
        
        # Wait a moment for server to fully initialize
        sleep 2
        
        # Start Electron app
        start_electron
        
        echo ""
        print_success "🎉 Remote Desktop system is now running!"
        echo ""
        print_status "📱 Access points:"
        echo "  • Electron Host App: Running in desktop application"
        echo "  • Web Client:        http://localhost:3000"
        echo "  • Web Host:          http://localhost:3000/host"
        echo ""
        print_status "🔧 How to use:"
        echo "  1. Use the Electron app to create a session"
        echo "  2. Share the session ID with the client"
        echo "  3. Client connects via web browser"
        echo "  4. Approve the connection in the Electron app"
        echo ""
        print_warning "Press Ctrl+C to stop all services"
        
        # Wait for both processes
        wait
        ;;
esac
