{"name": "remote-desktop-app", "version": "1.0.0", "description": "Open-source remote desktop application similar to TeamViewer", "main": "host/main.js", "scripts": {"start": "node server/server.js", "dev": "nodemon server/server.js", "electron": "electron .", "electron-dev": "NODE_ENV=development electron .", "host": "electron host/main.js", "build": "electron-builder", "build-host": "electron-builder", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "test": "jest", "postinstall": "electron-builder install-app-deps"}, "keywords": ["remote-desktop", "screen-sharing", "webrtc", "nodejs", "electron"], "author": "Your Name", "license": "MIT", "dependencies": {"auto-launch": "^5.0.6", "cors": "^2.8.5", "electron-store": "^8.2.0", "express": "^4.18.2", "helmet": "^7.1.0", "screenshot-desktop": "^1.15.0", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"electron": "^28.1.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "build": {"appId": "com.remotedesktop.host", "productName": "Remote Desktop Host", "directories": {"output": "dist", "buildResources": "build"}, "files": ["host/**/*", "shared/**/*", "assets/**/*", "node_modules/**/*", "!node_modules/electron/**/*", "!node_modules/electron-builder/**/*"], "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}], "mac": {"category": "public.app-category.utilities", "icon": "assets/icon.icns", "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "repository": {"type": "git", "url": "https://github.com/yourusername/remote-desktop-app.git"}}