<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/app-update.yml</key>
		<data>
		rF+g9ADr0ent7Z6z/E0lMnU6Nbs=
		</data>
		<key>Resources/app.asar</key>
		<data>
		JKKsrxb785hQFY2ezNTsHLWZQ8A=
		</data>
		<key>Resources/assets/icon.png</key>
		<data>
		rcg7GeeTSRscbqD9i0bNnzLlkvw=
		</data>
		<key>Resources/assets/icon.svg</key>
		<data>
		q9BMyyLFI1msUisviFbkewXMnvQ=
		</data>
		<key>Resources/electron.icns</key>
		<data>
		yBsjVAviSqArVhNx5Jx3gIgxq5A=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/Electron Framework.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			KrAcKygqzwOlr3TQsR46yeDebYA=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Electron.framework" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Mantle.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			l+343E18SK0ActR/hNz6RztgXe8=
			</data>
			<key>requirement</key>
			<string>identifier "org.mantle.Mantle" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/ReactiveObjC.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			r0ojsGmRPcJkksHBcge6t1jz3ms=
			</data>
			<key>requirement</key>
			<string>identifier "com.electron.reactive" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Remote Desktop Host Helper (GPU).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			fPmN/0nEuyQf7/JDLVNubEDLEA4=
			</data>
			<key>requirement</key>
			<string>identifier "com.remotedesktop.host.helper.GPU" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Remote Desktop Host Helper (Plugin).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			h2amafXETbTUqpCDyuWnNQXdZQc=
			</data>
			<key>requirement</key>
			<string>identifier "com.remotedesktop.host.helper.Plugin" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Remote Desktop Host Helper (Renderer).app</key>
		<dict>
			<key>cdhash</key>
			<data>
			JZluLAcE0Ie7N/TFvHlr3KmyWRA=
			</data>
			<key>requirement</key>
			<string>identifier "com.remotedesktop.host.helper.Renderer" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Remote Desktop Host Helper.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			poFLNQyxnh745AUkhyMtR7fcHmE=
			</data>
			<key>requirement</key>
			<string>identifier "com.remotedesktop.host.helper" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Frameworks/Squirrel.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			fTdaZiqteUjcEO172WYCjKLD3yQ=
			</data>
			<key>requirement</key>
			<string>identifier "com.github.Squirrel" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.*********] /* exists */</string>
		</dict>
		<key>Resources/app-update.yml</key>
		<dict>
			<key>hash2</key>
			<data>
			BQxdhlN8p5du2huuJ9Z51nCqVpqhmSC21QQ8V2duaUc=
			</data>
		</dict>
		<key>Resources/app.asar</key>
		<dict>
			<key>hash2</key>
			<data>
			hkhHk3fce90GnsoAn1P+DF5jHekHsUL1zBYsPEyvFNo=
			</data>
		</dict>
		<key>Resources/assets/icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			AbpHGcgLb+kRsJGnwFEktk7uzpZOCcBY74+YBdrKVGs=
			</data>
		</dict>
		<key>Resources/assets/icon.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ODnBD08ApA6+NbVlxSi6krq9/INC2Ea+6zk0X96CX5Y=
			</data>
		</dict>
		<key>Resources/electron.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			Wpp41UwVf1VnKv6jcDdGSFiof9XydvyCBnh/Nm7WhM8=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
