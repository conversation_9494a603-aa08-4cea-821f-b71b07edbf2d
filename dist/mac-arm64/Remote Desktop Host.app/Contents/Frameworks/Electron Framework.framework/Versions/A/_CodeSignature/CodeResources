<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		f09/JO0wNmb653bhgz1r9eqP1aA=
		</data>
		<key>Resources/MainMenu.nib</key>
		<data>
		kGuqJL15gkToanSQ/frSnS0EtAw=
		</data>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			53tWz259SPWrcmn4zcwXsU+uc+M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Y2OlnPl8iV/x/E0G1ZO6mUnIaK8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Kj3nsdof3z6BtmHxgBbOBclgcNg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+dgM+bfJH2r2Vt0E1EiQHbYu8go=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			6wSc2di0AIXbj7deCmL6nIcsZRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			EIC4hyHUsBzfPPoooDo4Yc4go+I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<data>
		mAOJqDcfK3+voJ11PIA72p1jteE=
		</data>
		<key>Resources/chrome_200_percent.pak</key>
		<data>
		hNp3JJWVBxX5q8FW9Hw7TufUges=
		</data>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AhgwACIRpHi2VYBmQNW6KWQ8lJ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			49AeOt+yc+3fqhgsJE8xhSmU0Bs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			wCAfHgAB0qtoCnB9X/TyJH+7aS0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VM1/+ExNIzuUcrZtK4dzAS4rXKE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			i/Avq+4M+haa+VNexJGY72EIztM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xhIT3wT+nlBP5hxOxdMINChAwZM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			mXLg/uTlX3dTMRnlArxKhOzHX2o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			16LSER0giEOOmxfYfa36EdkA4gA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rW0cyeduiGZayq+qufQv7/9vzUE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xJO+YIDI8F2D1H7pz+PkoRw3YtE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			dH6YFx9Z8KeObiTIf9AaNm2cuyU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			TeiKt9us0L6njyCmBeQyldrg5SM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			puzzoBCDllodChsCFPGcEf/yD9A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			+JLgxPN+1gToFAA9fXAG9cEZhiU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			0SjQ35MnHMvTptwHZB7g0UjqGvI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YdqWJ8gg9Fl6XsqdVOeSdy0LGeY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			hKm+JPvU47r2HU8xpd/4RKgC5iU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			SswA0GkyS7lNjqblswjKwHJ80O4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<data>
		qFOfxaIzVY7fomSjT3r2GHw/DU8=
		</data>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			ioby9Irjfa0CtkIlW0Q9CulppJQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			rk0mwalUifv+6IixmM7M/3GlrxE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			j3pZR+BH6Pb3C/lh65rtR5M8pes=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			NGXfzaExPzIKe2PVvnNTDKKA3II=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			RWFsSxa0XiBufYKRHs8DXCdkx2k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Xls1ruHUNB+Xt5AE3JVFnssF1KQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			QjRwdq3ZzpqFSTX6BEPAyYwUwW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			m9d57NX182PyfJMMjyxRegZrxYo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			0QkKAAqdyQvmnUM1ykuevML+ueE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			zVxffELbJ6+1VBHGHckOB28qWME=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xEaQvqX+tAm0dtIv5VNtdwKwP6k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			APVOV0SargsHmrqyCMVRVz+eVNw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			RtWflTVKLoa68yd7GYnuMCvRa1o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Vz2E8fptt09GxjIWX14EXiVadKc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			UirUbtpqDfdwED/YgEbe1P0f8o4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<data>
		IiYO7HUy0C0z9Y7kzum3N2pHk9U=
		</data>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			V5PbX0pCYl+rWfQOxfqgUjrLUOE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			UuD/stMXcs++ASn5zkqUaDn5qcg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			w3Osr0Sy2zIW0pjEgZlNajY+2f4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Bu/d8xqnKXIgdIHBAtexicNWaxw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			Kq0yNyhhl1o/XjH8jpPJUbwzgnA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			QvD3GY1loJfJ58D/BmsGAZijzKw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			xUihDjFectBJ0pTyafiULdagUvw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			YwpJ6mLr9uv4ZZGYR36PfK0mRCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			GhvckXRqgyGj6mFeIVM5OpEFjWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			5awFEg7i9WkkPT6skwjjyvBcrlY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			0U6ecDxGE/maTd+8mvp7mSg/20A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			U6LRsyfG3DKylpOEasjtWVFRN+A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			VpzJeTmT+j/sN4XLzD0ylyr4c/U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<data>
		doOUhEYjCSdrcmvqYDH4OjkoSLU=
		</data>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			AmUCEZM8AxqSlxpia80R9IKDMkk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			9khUMmdYu+FdI4tqw7KIIotDOvo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash</key>
			<data>
			8kDyW6Jv3Gz7DqS+/oqbuv5VkHY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Helpers/chrome_crashpad_handler</key>
		<dict>
			<key>cdhash</key>
			<data>
			uK4L6w7hdKqjCK2ZRFT4SskUPgs=
			</data>
			<key>requirement</key>
			<string>identifier "chrome_crashpad_handler" and anchor apple generic and certificate leaf[subject.CN] = "Apple Development: <EMAIL> (8DSP9K364D)" and certificate 1[field.1.2.840.113635.100.6.2.1] /* exists */</string>
		</dict>
		<key>Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			sJrENLa83jt3y9G3opSvZXSJxQt6Es09Nokty2DkmRk=
			</data>
		</dict>
		<key>Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			y7gMqwaXAt8wKby1qPp+Iobv1Uu75tbDp6ZB+Bb05G0=
			</data>
		</dict>
		<key>Libraries/libffmpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			8EJBiNIkMrPIWPq6dm+8QWtdDNCtfpBgE48qyo3I77Y=
			</data>
		</dict>
		<key>Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			gWT0vqFyHiqhPzg2eSPVbKfd8s05YdnHnVRSqUjxckM=
			</data>
		</dict>
		<key>Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			a9UwfQ+zip/cxaVt67Zc4b15qgfEV7PccEPUIvaKnFI=
			</data>
		</dict>
		<key>Resources/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			pdL4G86pddwplV8OvauLD6Gpkqye9P27g6JLvgxDeAM=
			</data>
		</dict>
		<key>Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RkbYRxm6t8NmI6z0NN8LV9Tkd+7HIg+r5fm48626jIg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RWHNAi0BLUYKYY400utwE2huxmu2jqlJFihYklweI08=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Gm1FRMPqVtBoIAqnubeB1rPVMbcBBbtnuNNRQhJ9YJc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			tjGz2EKlm8ix8cr2IpzT3WtvebJETKg7GonlSB2u/NI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			zKHCoU8ql++RbFGw1hUuVvezIIDl4QcNHNQyzIWAfrI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			x1iQdrVUeqIKgzAU+5byvQkMCNSNJDEqe3BJ+/SBvLM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			mD+NrbpK9l3lA3SdqWgNRGOAT7kH1nOxafxEhdTmP8U=
			</data>
		</dict>
		<key>Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			IWr8qRuVp7AjyK1WsufVShb9CDaK6EvW4mA4pr6FILQ=
			</data>
		</dict>
		<key>Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DcocQm8fyBZ5e8+rDVq4RLp/SK//LW7WdR5x86+7X94=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xXMNHybc+w6gJR3w2txFlaOeHIkk7WFtcOae/ka/KLQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			bJY3kLSDbyBIoEZSt8/Z9+zx+PD86XY5zyA1Qko5a8I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			76ttmEgeC1n98GiojPApOxI4zaAmB8CzDi3pFgRr2J8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HjgRL+9b6l10QkWxiy0B2nC3Z6chSSza1kwdxCHjx/c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			QPJX13M0MHxqymmaEqrVlR3W71y3LjSo90833JYYjYc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PegMG0Nl+WGhQNq8Bk/7d6qHfWHaI2AMf9hU6uTmUlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			njL/gdgL9Nmboj+NItL6KFgIak6vVBeYDi4VUDJH9tI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Jfv8b27IosohONPGwdcpVuhO2Q1K4YyC18dg7RZGL84=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			EpJqccBXHe7LjE7gJroJT80UqYe7IVFJwBjHekb6ORk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HptUg0npfEbKXeyY8KVMJ/QdLuf7axaWxFeNX0gBJ8c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			2AtNAc7vQ5hbT0DtUfM8X45wE3AMqnZViUbp550RmWU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			vOIQ1iyErSFMBx/Eri2qj7xoEomf5CWVGFSyDTShH9I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LpAV0LUpxoe+VpQfnRXOM/GvVf2Jog3ZnVZJD/P6g6Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			CJaddTrOV9U2SxfusVb4BpUpWtrm4Fsd3yFr9T0Zjk4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			95g/WpU97K7JklrUBEP8NZM0BYjathMWcoxW6AU5WFk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xvaipfiU+koy7IcsHmOt/o7T2lIVe5NDCOccMyXkSgs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			wTS0muy5qwCv7JPaoMScDmMBfu32hKgcY0tU5+mAifs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			9RcKorOI0jvr+YeE3UiKm8t0FHA4SmqajXomONdo3vs=
			</data>
		</dict>
		<key>Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			y3FYh5WtEWk9U+k7XGiFKnavJzsWWU/QeN2sKany6Tk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			rpB8VX51IHsXbLWIBJeC4/2+mEs41eJ0dXYOKxM9mR8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			IGmHrkBGRbuajBzmbKGJCV7FV0aiZuzmDuS5XVM8xI8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			7n0haBPx2Yh3Xa/zpSROUz7+2vvIqPBKs9UgYXOgXmI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			bVWYfo2/04MR+7H6uznhaPChe3xPQ1UHlXTVEXN1OTE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			rtNr8Qm9YOjjQaz/XSNKg7ZC5oFPeb+ZKgefGdgcs+g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			J5d/KKtUWmVmG7bHfD4Z4WVfZXH7Q8JJgFZX8/l82ho=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			VE5Fh4EccNnPFEEQVMQtZqXDUQjWT5JZfVHLmE4gofY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			okOI2pO81rYcIKpJk46+JQe/5G6PjFJlf24jBXlElew=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			OhIoBC0oFj8+FdzccXWQQp58qgZUvwfoqih82G4Gc/o=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			J2/A2/YcdJBZ0czOglMAWyJKtwinqYqY0gkYU61pd70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			f6HFKbQnefAb9asQKM9EB3pMgjIoLg96ezXvZgTvpeQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			16JQzitVNi8XtiAf8X11Co1WYTEyQ78UqJxoK1s52lo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			HZtlVUIZSiFtouHTuWf6X2RrkxkfrYjxxQvxKmwcZKI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Nb4syaSwcyfW5FVHeKned354NVbX0fgFcYs95t2zjRU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			qKYwdoxHSUrSHy+QpCMKFDyos5Eb/69cTNqnOqzDCK8=
			</data>
		</dict>
		<key>Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			SHNbjsclc0nd38Ef762oq5aBkqOVisOCyIOyKyYD1OY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Yj2EVFAuNaqllg/DJPj3eScmMPpVaHTEPAARubJ+8HE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			jHa2CNCWEUtjGZbLs/9pPc7pwitnaqKERzBCdRSBgCU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WJz+tPGeIK2jRh9h56L1zWY1togRmzbqUzfH4dJ69UE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			aSN499rrgoB6k9a1hQhBPforKbGjbjet/NlKHmFOcpI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			oNg8+sXk959zoPNIiyhJ03QfrXFnvJuuFMJVTyEoeu0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			DVdwAP+nSD3k/vybQVozO6vws7T2tP0t6uLkMIkRu04=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			OVkwqZuNYkTHvRRScvRt1Iv7Z9TBvVywbJpzlPQLz70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			77NGgD5tRon82ErtKDRCmJbdP5+tYqeqcjKYfr7TvBk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1NiDaHJ24BBHNx7P8sdZFggRZ+/JLX/yua1hFMzp0CI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1RCTJazrNND/M4l4adz3gY3R90htlg15Z2JdBrOqWW4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			73Ri2YeK7qvyRd6Wd5XDp+UUhd8dpIwaL0A1rV2wWdg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			pe+NInfIv9ziy1qAJirg1xzzq8PY4+pEYZ7yhIqPQno=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			IrVYXT+3lUAjO47lh0lz8t94r9TcoiJKKmUzUhMmkfU=
			</data>
		</dict>
		<key>Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6c2rZfQSDAR9IKqFHZBkQKof2wTwuZ+nXtxtJ4DEpSg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			B7RxgCMVF01GxLCatB0aKWMDpx8qaP+5D8K6CCH7Sio=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PRLIeNQfey2JjVcuhQjL7OXo4lZo0eZQIRw6I7HCFdc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
