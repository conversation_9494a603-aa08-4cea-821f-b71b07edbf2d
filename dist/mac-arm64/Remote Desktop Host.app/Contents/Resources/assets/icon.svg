<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2ecc71;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#27ae60;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#grad1)" stroke="#2c3e50" stroke-width="8"/>
  
  <!-- Monitor screen -->
  <rect x="140" y="160" width="232" height="140" rx="12" ry="12" fill="#2c3e50" stroke="#34495e" stroke-width="4"/>
  <rect x="150" y="170" width="212" height="120" rx="6" ry="6" fill="#ecf0f1"/>
  
  <!-- Screen content (connection lines) -->
  <g stroke="#3498db" stroke-width="3" fill="none">
    <line x1="170" y1="190" x2="220" y2="190"/>
    <line x1="170" y1="210" x2="250" y2="210"/>
    <line x1="170" y1="230" x2="200" y2="230"/>
    <line x1="170" y1="250" x2="240" y2="250"/>
    <line x1="170" y1="270" x2="210" y2="270"/>
  </g>
  
  <!-- Connection indicator -->
  <circle cx="320" cy="200" r="8" fill="url(#grad2)"/>
  <circle cx="320" cy="200" r="12" fill="none" stroke="#27ae60" stroke-width="2" opacity="0.6">
    <animate attributeName="r" values="12;20;12" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="opacity" values="0.6;0;0.6" dur="2s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Monitor stand -->
  <rect x="230" y="300" width="52" height="20" rx="4" ry="4" fill="#34495e"/>
  <rect x="200" y="320" width="112" height="12" rx="6" ry="6" fill="#2c3e50"/>
  
  <!-- Remote cursor -->
  <g transform="translate(280,240)">
    <path d="M0,0 L0,16 L4,12 L8,16 L12,12 L6,6 L16,0 Z" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>
  </g>
  
  <!-- Connection waves -->
  <g stroke="#3498db" stroke-width="2" fill="none" opacity="0.7">
    <path d="M 100 256 Q 150 220 200 256 Q 250 292 300 256 Q 350 220 400 256">
      <animate attributeName="d" 
               values="M 100 256 Q 150 220 200 256 Q 250 292 300 256 Q 350 220 400 256;
                       M 100 256 Q 150 240 200 256 Q 250 272 300 256 Q 350 240 400 256;
                       M 100 256 Q 150 220 200 256 Q 250 292 300 256 Q 350 220 400 256" 
               dur="3s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Security lock -->
  <g transform="translate(380,350)">
    <rect x="0" y="8" width="24" height="16" rx="2" ry="2" fill="#f39c12" stroke="#e67e22" stroke-width="1"/>
    <path d="M 6 8 Q 6 2 12 2 Q 18 2 18 8" fill="none" stroke="#e67e22" stroke-width="2"/>
    <circle cx="12" cy="16" r="2" fill="#e67e22"/>
  </g>
</svg>
