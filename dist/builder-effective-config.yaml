directories:
  output: dist
  buildResources: build
appId: com.remotedesktop.host
productName: Remote Desktop Host
files:
  - filter:
      - host/**/*
      - shared/**/*
      - assets/**/*
      - node_modules/**/*
      - '!node_modules/electron/**/*'
      - '!node_modules/electron-builder/**/*'
extraResources:
  - from: assets/
    to: assets/
    filter:
      - '**/*'
mac:
  category: public.app-category.utilities
  icon: assets/icon.icns
  hardenedRuntime: true
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: assets/icon.ico
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: deb
      arch:
        - x64
  icon: assets/icon.png
  category: Network
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
electronVersion: 28.3.3
